# MAUI Troubleshooting Guide

## 🚨 Common MAUI Build Issues & Solutions

### Issue: "MAUI0000: Failed to compute hash for file"

This error typically occurs when the MAUI Resizetizer cannot process resource files properly.

#### ✅ **SOLUTION 1: Use API-Only Build (Recommended for Testing)**

If you want to test the core functionality without dealing with MAUI setup issues:

```bash
# macOS/Linux
chmod +x build-api-only.sh
./build-api-only.sh

# Windows
build-api-only.bat
```

This will build and run just the backend API, which you can test with:
- Swagger UI at `https://localhost:7001/swagger`
- Postman or any HTTP client
- Browser for GET endpoints

#### ✅ **SOLUTION 2: Install MAUI Workloads**

```bash
# Install MAUI workloads
dotnet workload install maui

# If that fails, try updating first
dotnet workload update
dotnet workload install maui
```

#### ✅ **SOLUTION 3: Use Visual Studio 2022**

The easiest way to work with MA<PERSON> is using Visual Studio 2022 with the MAUI workload:

1. **Install Visual Studio 2022** (Community, Professional, or Enterprise)
2. **During installation, select**:
   - .NET Multi-platform App UI development
   - ASP.NET and web development
3. **Open the solution** in Visual Studio
4. **Build and run** from the IDE

#### ✅ **SOLUTION 4: Fix Resource Issues**

The current project has been configured to avoid resource issues by:
- Commenting out problematic resource references
- Using default MAUI icons and splash screens
- Removing custom font references

If you want to add custom resources later:
1. Use actual binary files (PNG, SVG, TTF) instead of text placeholders
2. Ensure proper file formats and sizes
3. Update the project file gradually

### Issue: "Type 'ViewModel' is not usable as an object element"

#### ✅ **SOLUTION: Already Fixed**

This issue has been resolved by:
- Removing direct ViewModel instantiation from XAML
- Using dependency injection in page constructors
- Properly registering all services and ViewModels

### Issue: Missing NuGet Packages

#### ✅ **SOLUTION: Package Restore**

```bash
# Clear NuGet cache
dotnet nuget locals all --clear

# Restore packages
dotnet restore --force

# If specific packages are missing
dotnet add package PackageName
```

## 🎯 **Recommended Development Approach**

### Phase 1: API Development & Testing ✅
1. **Start with API-only build** using `build-api-only.sh`
2. **Test all endpoints** using Swagger UI
3. **Verify database operations** work correctly
4. **Complete backend functionality** before moving to UI

### Phase 2: MAUI Setup (Optional)
1. **Install Visual Studio 2022** with MAUI workload
2. **Open solution in Visual Studio**
3. **Build MAUI project** from IDE
4. **Test on emulator/device**

### Phase 3: Alternative UI Options
If MAUI continues to cause issues, consider:
1. **Blazor MAUI** - Web-based UI in MAUI container
2. **Xamarin.Forms** - Previous generation cross-platform
3. **Native apps** - Separate iOS/Android projects
4. **Web app** - Progressive Web App (PWA)

## 🔧 **Platform-Specific Requirements**

### Windows Development
- **Visual Studio 2022** (recommended)
- **Windows 10 version 1809** or higher
- **Windows SDK** (installed with Visual Studio)

### macOS Development
- **Visual Studio for Mac** or **Visual Studio Code**
- **Xcode** (for iOS development)
- **macOS 10.15** or higher

### Android Development
- **Android SDK** (installed with Visual Studio)
- **Android emulator** or physical device
- **Java Development Kit (JDK)**

### iOS Development (macOS only)
- **Xcode** (latest version)
- **iOS Simulator** or physical device
- **Apple Developer Account** (for device deployment)

## 🚀 **Quick Start Without MAUI Issues**

### Option 1: API + Web Frontend
```bash
# Build and run API
./build-api-only.sh
cd src/PetSitterConnect.Api && dotnet run

# Create a simple web frontend using the API
# Use React, Vue, Angular, or plain HTML/JavaScript
```

### Option 2: API + Postman/Swagger Testing
```bash
# Build and run API
./build-api-only.sh
cd src/PetSitterConnect.Api && dotnet run

# Open browser to https://localhost:7001/swagger
# Test all endpoints interactively
```

### Option 3: API + Mobile Web App
```bash
# Build and run API
./build-api-only.sh
cd src/PetSitterConnect.Api && dotnet run

# Create a responsive web app that works on mobile browsers
# Use Bootstrap, Tailwind CSS, or similar frameworks
```

## 📞 **Getting Help**

### If API Build Fails
1. **Check .NET version**: `dotnet --version` (should be 8.0.x)
2. **Clear NuGet cache**: `dotnet nuget locals all --clear`
3. **Restore packages**: `dotnet restore --force`
4. **Check error messages** for specific package issues

### If MAUI Build Fails
1. **Try API-only build first** to verify core functionality
2. **Install Visual Studio 2022** with MAUI workload
3. **Use Visual Studio IDE** instead of command line
4. **Check MAUI workload**: `dotnet workload list`

### If Database Issues
1. **Check SQL Server LocalDB** is installed
2. **Update connection string** in `appsettings.json`
3. **Run migrations**: `dotnet ef database update`
4. **Check Entity Framework tools**: `dotnet ef --version`

---

## ✅ **Current Status**

- ✅ **Backend API**: Fully functional and ready to use
- ✅ **Database Models**: Complete with relationships
- ✅ **Authentication**: JWT-based security implemented
- ✅ **API Documentation**: Swagger UI available
- ✅ **Core Features**: User management, pets, requests, sitter profiles
- 🚧 **MAUI UI**: May require additional setup depending on environment

**The core application functionality is complete and testable via the API!**
