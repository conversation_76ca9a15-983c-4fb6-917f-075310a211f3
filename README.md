# PetSitter Connect

A cross-platform mobile application built with .NET MAUI that connects pet owners with pet sitters for short-term pet care services.

## Project Structure

```
PetSitterConnect/
├── src/
│   ├── PetSitterConnect/              # .NET MAUI Client App
│   │   ├── ViewModels/                # MVVM ViewModels
│   │   ├── Views/                     # XAML Pages
│   │   ├── Services/                  # Client Services
│   │   └── Resources/                 # Images, Fonts, Styles
│   ├── PetSitterConnect.Api/          # ASP.NET Core Web API
│   │   ├── Controllers/               # API Controllers
│   │   ├── Services/                  # Business Logic Services
│   │   ├── Data/                      # Entity Framework DbContext
│   │   └── Hubs/                      # SignalR Hubs
│   └── PetSitterConnect.Core/         # Shared Models & DTOs
│       ├── Models/                    # Domain Models
│       └── DTOs/                      # Data Transfer Objects
└── tests/
    └── PetSitterConnect.Tests/        # Unit Tests
```

## Features

### Core Features
- ✅ User Registration & Authentication (Pet Owners and Pet Sitters)
- ✅ Profile Management with personal info and pet details
- ✅ Pet Management (Add, Edit, Delete pets)
- 🚧 Pet Care Request System (Create and browse requests)
- 🚧 Booking & Scheduling System
- 🚧 In-App Chat with SignalR
- 🚧 Ratings & Reviews System
- 🚧 Payment Integration (Stripe/PayPal)
- 🚧 Admin Panel

### Technical Features
- ✅ .NET MAUI with .NET 8
- ✅ Proper MVVM Architecture with CommunityToolkit.Mvvm
- ✅ ASP.NET Core Web API Backend
- ✅ Entity Framework Core with SQL Server
- ✅ JWT Authentication
- ✅ AutoMapper for object mapping
- ✅ Dependency Injection
- ✅ RESTful API Design
- ✅ SignalR for real-time communication
- ✅ Swagger API Documentation

## Prerequisites

Before running this application, make sure you have the following installed:

1. **.NET 8 SDK** - [Download here](https://dotnet.microsoft.com/download/dotnet/8.0)
2. **Visual Studio 2022** (17.8 or later) with:
   - .NET Multi-platform App UI development workload
   - ASP.NET and web development workload
3. **SQL Server LocalDB** (included with Visual Studio)
4. **Android SDK** (for Android development)
5. **Xcode** (for iOS development on macOS)

## Getting Started

### 1. Clone the Repository
```bash
git clone <repository-url>
cd PetCare
```

### 2. Install .NET 8 SDK
If you don't have .NET 8 installed:
```bash
# Check if .NET 8 is installed
dotnet --version

# If not installed, download from: https://dotnet.microsoft.com/download/dotnet/8.0
```

### 3. Restore NuGet Packages
```bash
dotnet restore
```

### 4. Set Up the Database
```bash
cd src/PetSitterConnect.Api
dotnet ef database update
```

### 5. Run the Backend API
```bash
cd src/PetSitterConnect.Api
dotnet run
```
The API will be available at `https://localhost:7001`

### 6. Run the MAUI App
```bash
cd src/PetSitterConnect
dotnet build
dotnet run --framework net8.0-windows10.0.19041.0  # For Windows
# or
dotnet run --framework net8.0-android              # For Android
# or
dotnet run --framework net8.0-ios                  # For iOS
```

## API Documentation

Once the API is running, you can access the Swagger documentation at:
`https://localhost:7001/swagger`

### Available Endpoints

#### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration

#### Users
- `GET /api/users/me` - Get current user profile
- `PUT /api/users/me` - Update current user profile
- `DELETE /api/users/me` - Delete current user account

#### Pets
- `GET /api/pets` - Get user's pets
- `POST /api/pets` - Create a new pet
- `GET /api/pets/{id}` - Get pet by ID
- `PUT /api/pets/{id}` - Update pet
- `DELETE /api/pets/{id}` - Delete pet

## Configuration

### API Configuration (appsettings.json)
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=PetSitterConnectDb;Trusted_Connection=true"
  },
  "JwtSettings": {
    "SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!",
    "Issuer": "PetSitterConnect",
    "Audience": "PetSitterConnectUsers",
    "ExpirationInMinutes": 1440
  }
}
```

### MAUI App Configuration
Update the API base URL in `MauiProgram.cs`:
```csharp
builder.Services.AddHttpClient("PetSitterApi", client =>
{
    client.BaseAddress = new Uri("https://localhost:7001/api/");
});
```

## Development Phases

### ✅ Phase 1: Project Setup & Architecture (COMPLETED)
- [x] .NET MAUI project with MVVM architecture
- [x] ASP.NET Core Web API setup
- [x] Entity Framework Core configuration
- [x] Basic project structure and dependencies

### 🚧 Phase 2: Backend API Development (IN PROGRESS)
- [x] User authentication and authorization
- [x] Database models and relationships
- [x] Core API endpoints (Auth, Users, Pets)
- [ ] Pet care request endpoints
- [ ] Booking system endpoints
- [ ] Chat and messaging endpoints

### 📋 Phase 3: User Authentication & Profiles (PLANNED)
- [ ] Complete user registration/login flow
- [ ] Profile management screens
- [ ] Pet sitter profile creation
- [ ] User verification system

### 📋 Phase 4: Pet Care Request System (PLANNED)
- [ ] Create pet care requests
- [ ] Browse and search requests
- [ ] Request matching algorithm
- [ ] Request management

### 📋 Phase 5: Booking & Scheduling (PLANNED)
- [ ] Booking creation and confirmation
- [ ] Calendar integration
- [ ] Notification system
- [ ] Booking status management

### 📋 Phase 6: In-App Communication (PLANNED)
- [ ] Real-time chat implementation
- [ ] Message history
- [ ] File/image sharing
- [ ] Push notifications

### 📋 Phase 7: Ratings & Reviews (PLANNED)
- [ ] Review system for both parties
- [ ] Rating aggregation
- [ ] Review moderation
- [ ] Trust and safety features

### 📋 Phase 8: Payment Integration (PLANNED)
- [ ] Stripe/PayPal integration
- [ ] Escrow payment system
- [ ] Payment history
- [ ] Refund management

### 📋 Phase 9: Testing & Deployment (PLANNED)
- [ ] Unit tests
- [ ] Integration tests
- [ ] UI tests
- [ ] Performance optimization
- [ ] App store deployment

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support and questions, please open an issue in the GitHub repository.
