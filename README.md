# PetSitter Connect

A cross-platform mobile application built with .NET MAUI that connects pet owners with pet sitters for short-term pet care services.

## Project Structure

```
PetSitterConnect/
├── src/
│   ├── PetSitterConnect/              # .NET MAUI Client App
│   │   ├── ViewModels/                # MVVM ViewModels
│   │   ├── Views/                     # XAML Pages
│   │   ├── Services/                  # Client Services
│   │   └── Resources/                 # Images, Fonts, Styles
│   ├── PetSitterConnect.Api/          # ASP.NET Core Web API
│   │   ├── Controllers/               # API Controllers
│   │   ├── Services/                  # Business Logic Services
│   │   ├── Data/                      # Entity Framework DbContext
│   │   └── Hubs/                      # SignalR Hubs
│   └── PetSitterConnect.Core/         # Shared Models & DTOs
│       ├── Models/                    # Domain Models
│       └── DTOs/                      # Data Transfer Objects
└── tests/
    └── PetSitterConnect.Tests/        # Unit Tests
```

## Features

### Core Features
- ✅ User Registration & Authentication (Pet Owners and Pet Sitters)
- ✅ Profile Management with personal info and pet details
- ✅ Pet Management (Add, Edit, Delete pets)
- ✅ Sitter Profile Management (Create and manage sitter profiles)
- ✅ Pet Care Request System (Create and browse requests)
- 🚧 Booking & Scheduling System
- 🚧 In-App Chat with SignalR
- 🚧 Ratings & Reviews System
- 🚧 Payment Integration (Stripe/PayPal)
- 🚧 Admin Panel

### Technical Features
- ✅ .NET MAUI with .NET 8
- ✅ Proper MVVM Architecture with CommunityToolkit.Mvvm
- ✅ ASP.NET Core Web API Backend
- ✅ Entity Framework Core with SQL Server
- ✅ JWT Authentication
- ✅ AutoMapper for object mapping
- ✅ Dependency Injection
- ✅ RESTful API Design
- ✅ SignalR for real-time communication
- ✅ Swagger API Documentation

## Prerequisites

Before running this application, make sure you have the following installed:

1. **.NET 8 SDK** - [Download here](https://dotnet.microsoft.com/download/dotnet/8.0)
2. **Visual Studio 2022** (17.8 or later) with:
   - .NET Multi-platform App UI development workload
   - ASP.NET and web development workload
3. **SQL Server LocalDB** (included with Visual Studio)
4. **Android SDK** (for Android development)
5. **Xcode** (for iOS development on macOS)

## Getting Started

### 🚀 Quick Start - API Only (Recommended)
```bash
# Build and test the backend API first (macOS/Linux)
chmod +x build-api-only.sh
./build-api-only.sh

# Or on Windows
build-api-only.bat

# Then start the API
cd src/PetSitterConnect.Api && dotnet run
# Visit: https://localhost:7001/swagger
```

### 📱 Full MAUI Build (Requires Additional Setup)
```bash
# For full MAUI app (may require Visual Studio 2022)
chmod +x build.sh
./build.sh

# Or on Windows
build.bat
```

**Note**: If you encounter MAUI build issues, see [MAUI_TROUBLESHOOTING.md](MAUI_TROUBLESHOOTING.md) for solutions.

### Manual Setup

### 1. Install .NET 8 SDK
Download and install .NET 8 SDK from: https://dotnet.microsoft.com/download/dotnet/8.0

Verify installation:
```bash
dotnet --version
# Should show 8.0.x
```

### 2. Clone and Setup
```bash
git clone <repository-url>
cd PetCare
dotnet restore
```

### 3. Run the Backend API
```bash
cd src/PetSitterConnect.Api
dotnet run
```
The API will be available at `https://localhost:7001`

### 4. Run the MAUI App
```bash
cd src/PetSitterConnect
dotnet run --framework net8.0-windows10.0.19041.0  # For Windows
# or
dotnet run --framework net8.0-android              # For Android (requires Android SDK)
# or
dotnet run --framework net8.0-ios                  # For iOS (requires Xcode on macOS)
```

### 5. Database Setup (Optional)
The app uses Entity Framework with SQL Server LocalDB by default. The database will be created automatically when you first run the API.

To manually create/update the database:
```bash
cd src/PetSitterConnect.Api
dotnet ef database update
```

## API Documentation

Once the API is running, you can access the Swagger documentation at:
`https://localhost:7001/swagger`

### Available Endpoints

#### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration

#### Users
- `GET /api/users/me` - Get current user profile
- `PUT /api/users/me` - Update current user profile
- `DELETE /api/users/me` - Delete current user account

#### Pets
- `GET /api/pets` - Get user's pets
- `POST /api/pets` - Create a new pet
- `GET /api/pets/{id}` - Get pet by ID
- `PUT /api/pets/{id}` - Update pet
- `DELETE /api/pets/{id}` - Delete pet

#### Pet Care Requests
- `GET /api/petcarerequests` - Get all pet care requests
- `GET /api/petcarerequests/my-requests` - Get current user's requests
- `POST /api/petcarerequests` - Create a new pet care request
- `GET /api/petcarerequests/{id}` - Get pet care request by ID
- `PUT /api/petcarerequests/{id}` - Update pet care request
- `DELETE /api/petcarerequests/{id}` - Delete pet care request

#### Sitter Profiles
- `GET /api/sitterprofiles/available` - Get available sitters
- `GET /api/sitterprofiles/me` - Get current user's sitter profile
- `POST /api/sitterprofiles` - Create sitter profile
- `PUT /api/sitterprofiles/me` - Update sitter profile
- `DELETE /api/sitterprofiles/me` - Delete sitter profile

## Configuration

### API Configuration (appsettings.json)
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=PetSitterConnectDb;Trusted_Connection=true"
  },
  "JwtSettings": {
    "SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!",
    "Issuer": "PetSitterConnect",
    "Audience": "PetSitterConnectUsers",
    "ExpirationInMinutes": 1440
  }
}
```

### MAUI App Configuration
Update the API base URL in `MauiProgram.cs`:
```csharp
builder.Services.AddHttpClient("PetSitterApi", client =>
{
    client.BaseAddress = new Uri("https://localhost:7001/api/");
});
```

## Development Phases

### ✅ Phase 1: Project Setup & Architecture (COMPLETED)
- [x] .NET MAUI project with MVVM architecture
- [x] ASP.NET Core Web API setup
- [x] Entity Framework Core configuration
- [x] Basic project structure and dependencies

### ✅ Phase 2: Backend API Development (COMPLETED)
- [x] User authentication and authorization
- [x] Database models and relationships
- [x] Core API endpoints (Auth, Users, Pets)
- [x] Pet care request endpoints
- [x] Sitter profile endpoints
- [x] SignalR hub for real-time chat

### ✅ Phase 3: User Authentication & Profiles (COMPLETED)
- [x] Complete user registration/login flow
- [x] Profile management screens
- [x] Pet sitter profile creation
- [x] User profile management

### ✅ Phase 4: Pet Care Request System (COMPLETED)
- [x] Create pet care requests
- [x] Browse and search requests
- [x] Request management UI
- [x] Pet selection for requests

### 📋 Phase 5: Booking & Scheduling (PLANNED)
- [ ] Booking creation and confirmation
- [ ] Calendar integration
- [ ] Notification system
- [ ] Booking status management

### 📋 Phase 6: In-App Communication (PLANNED)
- [ ] Real-time chat implementation
- [ ] Message history
- [ ] File/image sharing
- [ ] Push notifications

### 📋 Phase 7: Ratings & Reviews (PLANNED)
- [ ] Review system for both parties
- [ ] Rating aggregation
- [ ] Review moderation
- [ ] Trust and safety features

### 📋 Phase 8: Payment Integration (PLANNED)
- [ ] Stripe/PayPal integration
- [ ] Escrow payment system
- [ ] Payment history
- [ ] Refund management

### 📋 Phase 9: Testing & Deployment (PLANNED)
- [ ] Unit tests
- [ ] Integration tests
- [ ] UI tests
- [ ] Performance optimization
- [ ] App store deployment

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Troubleshooting

### Common Issues

#### 1. "Type 'BookingListViewModel' is not usable as an object element"
This error occurs when ViewModels are instantiated directly in XAML. The project has been configured to use dependency injection, so ViewModels are injected through page constructors.

**Solution**: The XAML files have been updated to remove direct ViewModel instantiation. Make sure you're using the latest version of the code.

#### 2. "Failed to compute hash for file 'Resources/Splash/splash.svg'"
This error occurs when resource files are missing.

**Solution**: All required resource files have been created. If you still see this error, make sure all files in the `Resources` folder are present.

#### 3. ".NET 8 SDK not found"
**Solution**: Install .NET 8 SDK from https://dotnet.microsoft.com/download/dotnet/8.0

#### 4. "Package restore failed"
**Solution**:
```bash
dotnet nuget locals all --clear
dotnet restore --force
```

#### 5. Database connection issues
**Solution**: Make sure SQL Server LocalDB is installed (comes with Visual Studio) or update the connection string in `appsettings.json` to use your preferred database.

### Platform-Specific Requirements

#### Windows
- .NET 8 SDK
- Visual Studio 2022 (recommended) or Visual Studio Code
- Windows 10 version 1809 or higher

#### macOS
- .NET 8 SDK
- Visual Studio for Mac or Visual Studio Code
- Xcode (for iOS development)

#### Android Development
- Android SDK
- Android emulator or physical device

#### iOS Development (macOS only)
- Xcode
- iOS Simulator or physical device

## Support

For support and questions, please open an issue in the GitHub repository.
