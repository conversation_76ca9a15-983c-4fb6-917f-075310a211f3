using AutoMapper;
using PetSitterConnect.Core.DTOs;
using PetSitterConnect.Core.Models;

namespace PetSitterConnect.Api.Mappings;

public class AutoMapperProfile : Profile
{
    public AutoMapperProfile()
    {
        // User mappings
        CreateMap<User, UserDto>()
            .ForMember(dest => dest.SitterProfile, opt => opt.MapFrom(src => src.SitterProfile));
        CreateMap<CreateUserDto, User>();
        CreateMap<UpdateUserDto, User>();

        // Pet mappings
        CreateMap<Pet, PetDto>();
        CreateMap<CreatePetDto, Pet>();
        CreateMap<UpdatePetDto, Pet>();

        // SitterProfile mappings
        CreateMap<SitterProfile, SitterProfileDto>();
        CreateMap<CreateSitterProfileDto, SitterProfile>();
        CreateMap<UpdateSitterProfileDto, SitterProfile>();

        // PetCareRequest mappings
        CreateMap<PetCareRequest, PetCareRequestDto>();
        CreateMap<CreatePetCareRequestDto, PetCareRequest>();
        CreateMap<UpdatePetCareRequestDto, PetCareRequest>();

        // Booking mappings
        CreateMap<Booking, BookingDto>();
        CreateMap<CreateBookingDto, Booking>();
        CreateMap<UpdateBookingDto, Booking>();

        // ChatMessage mappings
        CreateMap<ChatMessage, ChatMessageDto>();
        CreateMap<CreateChatMessageDto, ChatMessage>();

        // Review mappings
        CreateMap<Review, ReviewDto>();
        CreateMap<CreateReviewDto, Review>();

        // Payment mappings
        CreateMap<Payment, PaymentDto>();
        CreateMap<CreatePaymentDto, Payment>();
    }
}
