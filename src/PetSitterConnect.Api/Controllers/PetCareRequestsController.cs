using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PetSitterConnect.Api.Services;
using PetSitterConnect.Core.DTOs;
using System.Security.Claims;

namespace PetSitterConnect.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class PetCareRequestsController : ControllerBase
{
    private readonly IPetCareRequestService _petCareRequestService;

    public PetCareRequestsController(IPetCareRequestService petCareRequestService)
    {
        _petCareRequestService = petCareRequestService;
    }

    [HttpGet]
    public async Task<ActionResult<List<PetCareRequestDto>>> GetRequests([FromQuery] int page = 1, [FromQuery] int pageSize = 10)
    {
        var requests = await _petCareRequestService.GetRequestsAsync(page, pageSize);
        return Ok(requests);
    }

    [HttpGet("my-requests")]
    public async Task<ActionResult<List<PetCareRequestDto>>> GetMyRequests()
    {
        var userId = GetCurrentUserId();
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var requests = await _petCareRequestService.GetUserRequestsAsync(userId);
        return Ok(requests);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<PetCareRequestDto>> GetRequest(string id)
    {
        var request = await _petCareRequestService.GetRequestByIdAsync(id);
        if (request == null)
            return NotFound();

        return Ok(request);
    }

    [HttpPost]
    public async Task<ActionResult<PetCareRequestDto>> CreateRequest([FromBody] CreatePetCareRequestDto createRequestDto)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var userId = GetCurrentUserId();
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var request = await _petCareRequestService.CreateRequestAsync(userId, createRequestDto);
        if (request == null)
            return BadRequest("Failed to create pet care request");

        return CreatedAtAction(nameof(GetRequest), new { id = request.Id }, request);
    }

    [HttpPut("{id}")]
    public async Task<ActionResult<PetCareRequestDto>> UpdateRequest(string id, [FromBody] UpdatePetCareRequestDto updateRequestDto)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var request = await _petCareRequestService.UpdateRequestAsync(id, updateRequestDto);
        if (request == null)
            return NotFound();

        return Ok(request);
    }

    [HttpDelete("{id}")]
    public async Task<ActionResult> DeleteRequest(string id)
    {
        var success = await _petCareRequestService.DeleteRequestAsync(id);
        if (!success)
            return NotFound();

        return NoContent();
    }

    private string? GetCurrentUserId()
    {
        return User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
    }
}
