using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PetSitterConnect.Api.Services;
using PetSitterConnect.Core.DTOs;
using System.Security.Claims;

namespace PetSitterConnect.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class PetsController : ControllerBase
{
    private readonly IPetService _petService;

    public PetsController(IPetService petService)
    {
        _petService = petService;
    }

    [HttpGet]
    public async Task<ActionResult<List<PetDto>>> GetUserPets()
    {
        var userId = GetCurrentUserId();
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var pets = await _petService.GetUserPetsAsync(userId);
        return Ok(pets);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<PetDto>> GetPet(string id)
    {
        var pet = await _petService.GetPetByIdAsync(id);
        if (pet == null)
            return NotFound();

        return Ok(pet);
    }

    [HttpPost]
    public async Task<ActionResult<PetDto>> CreatePet([FromBody] CreatePetDto createPetDto)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var userId = GetCurrentUserId();
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var pet = await _petService.CreatePetAsync(userId, createPetDto);
        if (pet == null)
            return BadRequest("Failed to create pet");

        return CreatedAtAction(nameof(GetPet), new { id = pet.Id }, pet);
    }

    [HttpPut("{id}")]
    public async Task<ActionResult<PetDto>> UpdatePet(string id, [FromBody] UpdatePetDto updatePetDto)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var pet = await _petService.UpdatePetAsync(id, updatePetDto);
        if (pet == null)
            return NotFound();

        return Ok(pet);
    }

    [HttpDelete("{id}")]
    public async Task<ActionResult> DeletePet(string id)
    {
        var success = await _petService.DeletePetAsync(id);
        if (!success)
            return NotFound();

        return NoContent();
    }

    private string? GetCurrentUserId()
    {
        return User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
    }
}
