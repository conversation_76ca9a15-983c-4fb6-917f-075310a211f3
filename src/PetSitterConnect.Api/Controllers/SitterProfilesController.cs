using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PetSitterConnect.Api.Services;
using PetSitterConnect.Core.DTOs;
using System.Security.Claims;

namespace PetSitterConnect.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class SitterProfilesController : ControllerBase
{
    private readonly ISitterProfileService _sitterProfileService;

    public SitterProfilesController(ISitterProfileService sitterProfileService)
    {
        _sitterProfileService = sitterProfileService;
    }

    [HttpGet("available")]
    public async Task<ActionResult<List<SitterProfileDto>>> GetAvailableSitters([FromQuery] int page = 1, [FromQuery] int pageSize = 10)
    {
        var sitters = await _sitterProfileService.GetAvailableSittersAsync(page, pageSize);
        return Ok(sitters);
    }

    [HttpGet("me")]
    public async Task<ActionResult<SitterProfileDto>> GetMyProfile()
    {
        var userId = GetCurrentUserId();
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var profile = await _sitterProfileService.GetSitterProfileAsync(userId);
        if (profile == null)
            return NotFound("Sitter profile not found");

        return Ok(profile);
    }

    [HttpGet("{userId}")]
    public async Task<ActionResult<SitterProfileDto>> GetSitterProfile(string userId)
    {
        var profile = await _sitterProfileService.GetSitterProfileAsync(userId);
        if (profile == null)
            return NotFound();

        return Ok(profile);
    }

    [HttpPost]
    public async Task<ActionResult<SitterProfileDto>> CreateProfile([FromBody] CreateSitterProfileDto createProfileDto)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var userId = GetCurrentUserId();
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var profile = await _sitterProfileService.CreateSitterProfileAsync(userId, createProfileDto);
        if (profile == null)
            return BadRequest("Failed to create sitter profile. Profile may already exist.");

        return CreatedAtAction(nameof(GetMyProfile), profile);
    }

    [HttpPut("me")]
    public async Task<ActionResult<SitterProfileDto>> UpdateMyProfile([FromBody] UpdateSitterProfileDto updateProfileDto)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var userId = GetCurrentUserId();
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var profile = await _sitterProfileService.UpdateSitterProfileAsync(userId, updateProfileDto);
        if (profile == null)
            return NotFound();

        return Ok(profile);
    }

    [HttpDelete("me")]
    public async Task<ActionResult> DeleteMyProfile()
    {
        var userId = GetCurrentUserId();
        if (string.IsNullOrEmpty(userId))
            return Unauthorized();

        var success = await _sitterProfileService.DeleteSitterProfileAsync(userId);
        if (!success)
            return NotFound();

        return NoContent();
    }

    private string? GetCurrentUserId()
    {
        return User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
    }
}
