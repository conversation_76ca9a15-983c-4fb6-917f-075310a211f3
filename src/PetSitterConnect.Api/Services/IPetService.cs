using PetSitterConnect.Core.DTOs;

namespace PetSitterConnect.Api.Services;

public interface IPetService
{
    Task<List<PetDto>> GetUserPetsAsync(string userId);
    Task<PetDto?> GetPetByIdAsync(string petId);
    Task<PetDto?> CreatePetAsync(string userId, CreatePetDto createPetDto);
    Task<PetDto?> UpdatePetAsync(string petId, UpdatePetDto updatePetDto);
    Task<bool> DeletePetAsync(string petId);
}
