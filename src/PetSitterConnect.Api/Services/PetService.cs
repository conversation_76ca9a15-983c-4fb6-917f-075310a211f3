using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PetSitterConnect.Api.Data;
using PetSitterConnect.Core.DTOs;
using PetSitterConnect.Core.Models;

namespace PetSitterConnect.Api.Services;

public class PetService : IPetService
{
    private readonly ApplicationDbContext _context;
    private readonly IMapper _mapper;

    public PetService(ApplicationDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<List<PetDto>> GetUserPetsAsync(string userId)
    {
        var pets = await _context.Pets
            .Where(p => p.OwnerId == userId)
            .ToListAsync();

        return _mapper.Map<List<PetDto>>(pets);
    }

    public async Task<PetDto?> GetPetByIdAsync(string petId)
    {
        var pet = await _context.Pets.FindAsync(petId);
        return pet != null ? _mapper.Map<PetDto>(pet) : null;
    }

    public async Task<PetDto?> CreatePetAsync(string userId, CreatePetDto createPetDto)
    {
        var pet = new Pet
        {
            Name = createPetDto.Name,
            Type = createPetDto.Type,
            Breed = createPetDto.Breed,
            Age = createPetDto.Age,
            Size = createPetDto.Size,
            Color = createPetDto.Color,
            Description = createPetDto.Description,
            SpecialNeeds = createPetDto.SpecialNeeds,
            MedicalConditions = createPetDto.MedicalConditions,
            IsVaccinated = createPetDto.IsVaccinated,
            IsNeutered = createPetDto.IsNeutered,
            OwnerId = userId
        };

        _context.Pets.Add(pet);
        await _context.SaveChangesAsync();

        return _mapper.Map<PetDto>(pet);
    }

    public async Task<PetDto?> UpdatePetAsync(string petId, UpdatePetDto updatePetDto)
    {
        var pet = await _context.Pets.FindAsync(petId);
        if (pet == null)
            return null;

        pet.Name = updatePetDto.Name;
        pet.Breed = updatePetDto.Breed;
        pet.Age = updatePetDto.Age;
        pet.Size = updatePetDto.Size;
        pet.Color = updatePetDto.Color;
        pet.Description = updatePetDto.Description;
        pet.SpecialNeeds = updatePetDto.SpecialNeeds;
        pet.MedicalConditions = updatePetDto.MedicalConditions;
        pet.IsVaccinated = updatePetDto.IsVaccinated;
        pet.IsNeutered = updatePetDto.IsNeutered;
        pet.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        return _mapper.Map<PetDto>(pet);
    }

    public async Task<bool> DeletePetAsync(string petId)
    {
        var pet = await _context.Pets.FindAsync(petId);
        if (pet == null)
            return false;

        _context.Pets.Remove(pet);
        await _context.SaveChangesAsync();

        return true;
    }
}
