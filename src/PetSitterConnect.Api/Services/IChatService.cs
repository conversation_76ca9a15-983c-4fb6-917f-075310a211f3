namespace PetSitterConnect.Api.Services;

public interface IChatService
{
    // Placeholder for chat service methods
    Task<object> GetMessagesAsync(string bookingId);
}

public class ChatService : IChatService
{
    public async Task<object> GetMessagesAsync(string bookingId)
    {
        // Placeholder implementation
        await Task.CompletedTask;
        return new { Message = "Chat service not implemented yet" };
    }
}

// Placeholder DTOs
public class ChatMessageDto
{
    public string Id { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime SentAt { get; set; }
}

public class CreateChatMessageDto
{
    public string Message { get; set; } = string.Empty;
    public string BookingId { get; set; } = string.Empty;
}

public class ReviewDto
{
    public string Id { get; set; } = string.Empty;
    public int Rating { get; set; }
    public string? Comment { get; set; }
}

public class CreateReviewDto
{
    public int Rating { get; set; }
    public string? Comment { get; set; }
    public string BookingId { get; set; } = string.Empty;
}

public class PaymentDto
{
    public string Id { get; set; } = string.Empty;
    public decimal Amount { get; set; }
}

public class CreatePaymentDto
{
    public decimal Amount { get; set; }
    public string BookingId { get; set; } = string.Empty;
}
