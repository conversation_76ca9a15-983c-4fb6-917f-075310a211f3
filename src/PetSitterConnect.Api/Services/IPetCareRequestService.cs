using PetSitterConnect.Core.DTOs;

namespace PetSitterConnect.Api.Services;

public interface IPetCareRequestService
{
    Task<List<PetCareRequestDto>> GetRequestsAsync(int page = 1, int pageSize = 10);
    Task<List<PetCareRequestDto>> GetUserRequestsAsync(string userId);
    Task<PetCareRequestDto?> GetRequestByIdAsync(string requestId);
    Task<PetCareRequestDto?> CreateRequestAsync(string userId, CreatePetCareRequestDto createRequestDto);
    Task<PetCareRequestDto?> UpdateRequestAsync(string requestId, UpdatePetCareRequestDto updateRequestDto);
    Task<bool> DeleteRequestAsync(string requestId);
}
