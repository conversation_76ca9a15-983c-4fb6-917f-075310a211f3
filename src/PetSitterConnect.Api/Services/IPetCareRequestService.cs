using PetSitterConnect.Core.DTOs;

namespace PetSitterConnect.Api.Services;

public interface IPetCareRequestService
{
    Task<List<PetCareRequestDto>> GetRequestsAsync(int page = 1, int pageSize = 10);
    Task<List<PetCareRequestDto>> GetUserRequestsAsync(string userId);
    Task<PetCareRequestDto?> GetRequestByIdAsync(string requestId);
    Task<PetCareRequestDto?> CreateRequestAsync(string userId, CreatePetCareRequestDto createRequestDto);
    Task<PetCareRequestDto?> UpdateRequestAsync(string requestId, UpdatePetCareRequestDto updateRequestDto);
    Task<bool> DeleteRequestAsync(string requestId);
}

// Placeholder DTOs - these would be defined in the Core project
public class PetCareRequestDto
{
    public string Id { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public decimal Budget { get; set; }
    public string OwnerId { get; set; } = string.Empty;
    public string PetId { get; set; } = string.Empty;
}

public class CreatePetCareRequestDto
{
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public decimal Budget { get; set; }
    public string PetId { get; set; } = string.Empty;
}

public class UpdatePetCareRequestDto
{
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public decimal Budget { get; set; }
}
