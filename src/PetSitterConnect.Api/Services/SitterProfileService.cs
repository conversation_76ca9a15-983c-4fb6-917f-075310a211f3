using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PetSitterConnect.Api.Data;
using PetSitterConnect.Core.DTOs;
using PetSitterConnect.Core.Models;

namespace PetSitterConnect.Api.Services;

public class SitterProfileService : ISitterProfileService
{
    private readonly ApplicationDbContext _context;
    private readonly IMapper _mapper;

    public SitterProfileService(ApplicationDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<SitterProfileDto?> GetSitterProfileAsync(string userId)
    {
        var profile = await _context.SitterProfiles
            .Include(sp => sp.User)
            .FirstOrDefaultAsync(sp => sp.UserId == userId);

        return profile != null ? _mapper.Map<SitterProfileDto>(profile) : null;
    }

    public async Task<SitterProfileDto?> CreateSitterProfileAsync(string userId, CreateSitterProfileDto createProfileDto)
    {
        // Check if user already has a sitter profile
        var existingProfile = await _context.SitterProfiles
            .FirstOrDefaultAsync(sp => sp.UserId == userId);

        if (existingProfile != null)
            return null; // Profile already exists

        var profile = new SitterProfile
        {
            UserId = userId,
            YearsOfExperience = createProfileDto.YearsOfExperience,
            Experience = createProfileDto.Experience,
            Services = createProfileDto.Services,
            HourlyRate = createProfileDto.HourlyRate,
            DailyRate = createProfileDto.DailyRate,
            Availability = createProfileDto.Availability,
            MaxPets = createProfileDto.MaxPets,
            PreferredPetTypes = createProfileDto.PreferredPetTypes,
            PreferredPetSizes = createProfileDto.PreferredPetSizes,
            HasOwnPets = createProfileDto.HasOwnPets,
            HasYard = createProfileDto.HasYard,
            CanHostOvernight = createProfileDto.CanHostOvernight,
            CanTravelToOwner = createProfileDto.CanTravelToOwner,
            EmergencyContact = createProfileDto.EmergencyContact,
            References = createProfileDto.References
        };

        _context.SitterProfiles.Add(profile);
        await _context.SaveChangesAsync();

        return _mapper.Map<SitterProfileDto>(profile);
    }

    public async Task<SitterProfileDto?> UpdateSitterProfileAsync(string userId, UpdateSitterProfileDto updateProfileDto)
    {
        var profile = await _context.SitterProfiles
            .FirstOrDefaultAsync(sp => sp.UserId == userId);

        if (profile == null)
            return null;

        profile.YearsOfExperience = updateProfileDto.YearsOfExperience;
        profile.Experience = updateProfileDto.Experience;
        profile.Services = updateProfileDto.Services;
        profile.HourlyRate = updateProfileDto.HourlyRate;
        profile.DailyRate = updateProfileDto.DailyRate;
        profile.IsAvailable = updateProfileDto.IsAvailable;
        profile.Availability = updateProfileDto.Availability;
        profile.MaxPets = updateProfileDto.MaxPets;
        profile.PreferredPetTypes = updateProfileDto.PreferredPetTypes;
        profile.PreferredPetSizes = updateProfileDto.PreferredPetSizes;
        profile.HasOwnPets = updateProfileDto.HasOwnPets;
        profile.HasYard = updateProfileDto.HasYard;
        profile.CanHostOvernight = updateProfileDto.CanHostOvernight;
        profile.CanTravelToOwner = updateProfileDto.CanTravelToOwner;
        profile.EmergencyContact = updateProfileDto.EmergencyContact;
        profile.References = updateProfileDto.References;
        profile.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        return _mapper.Map<SitterProfileDto>(profile);
    }

    public async Task<bool> DeleteSitterProfileAsync(string userId)
    {
        var profile = await _context.SitterProfiles
            .FirstOrDefaultAsync(sp => sp.UserId == userId);

        if (profile == null)
            return false;

        _context.SitterProfiles.Remove(profile);
        await _context.SaveChangesAsync();

        return true;
    }

    public async Task<List<SitterProfileDto>> GetAvailableSittersAsync(int page = 1, int pageSize = 10)
    {
        var profiles = await _context.SitterProfiles
            .Include(sp => sp.User)
            .Where(sp => sp.IsAvailable && sp.User.IsActive)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return _mapper.Map<List<SitterProfileDto>>(profiles);
    }
}
