using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PetSitterConnect.Api.Data;
using PetSitterConnect.Core.DTOs;

namespace PetSitterConnect.Api.Services;

public class UserService : IUserService
{
    private readonly ApplicationDbContext _context;
    private readonly IMapper _mapper;

    public UserService(ApplicationDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<UserDto?> GetUserByIdAsync(string userId)
    {
        var user = await _context.AppUsers
            .Include(u => u.SitterProfile)
            .FirstOrDefaultAsync(u => u.Id == userId);

        return user != null ? _mapper.Map<UserDto>(user) : null;
    }

    public async Task<UserDto?> GetUserByEmailAsync(string email)
    {
        var user = await _context.AppUsers
            .Include(u => u.SitterProfile)
            .FirstOrDefaultAsync(u => u.Email == email);

        return user != null ? _mapper.Map<UserDto>(user) : null;
    }

    public async Task<UserDto?> UpdateUserAsync(string userId, UpdateUserDto updateUserDto)
    {
        var user = await _context.AppUsers.FindAsync(userId);
        if (user == null)
            return null;

        user.FirstName = updateUserDto.FirstName;
        user.LastName = updateUserDto.LastName;
        user.PhoneNumber = updateUserDto.PhoneNumber;
        user.Bio = updateUserDto.Bio;
        user.Address = updateUserDto.Address;
        user.City = updateUserDto.City;
        user.State = updateUserDto.State;
        user.ZipCode = updateUserDto.ZipCode;
        user.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        return _mapper.Map<UserDto>(user);
    }

    public async Task<bool> DeleteUserAsync(string userId)
    {
        var user = await _context.AppUsers.FindAsync(userId);
        if (user == null)
            return false;

        user.IsActive = false;
        user.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<List<UserDto>> GetUsersAsync(int page = 1, int pageSize = 10)
    {
        var users = await _context.AppUsers
            .Include(u => u.SitterProfile)
            .Where(u => u.IsActive)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return _mapper.Map<List<UserDto>>(users);
    }
}
