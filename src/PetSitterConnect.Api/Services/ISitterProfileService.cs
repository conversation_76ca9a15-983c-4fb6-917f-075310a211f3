using PetSitterConnect.Core.DTOs;

namespace PetSitterConnect.Api.Services;

public interface ISitterProfileService
{
    Task<SitterProfileDto?> GetSitterProfileAsync(string userId);
    Task<SitterProfileDto?> CreateSitterProfileAsync(string userId, CreateSitterProfileDto createProfileDto);
    Task<SitterProfileDto?> UpdateSitterProfileAsync(string userId, UpdateSitterProfileDto updateProfileDto);
    Task<bool> DeleteSitterProfileAsync(string userId);
    Task<List<SitterProfileDto>> GetAvailableSittersAsync(int page = 1, int pageSize = 10);
}
