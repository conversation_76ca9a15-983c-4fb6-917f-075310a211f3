using PetSitterConnect.Core.DTOs;

namespace PetSitterConnect.Api.Services;

public interface IUserService
{
    Task<UserDto?> GetUserByIdAsync(string userId);
    Task<UserDto?> GetUserByEmailAsync(string email);
    Task<UserDto?> UpdateUserAsync(string userId, UpdateUserDto updateUserDto);
    Task<bool> DeleteUserAsync(string userId);
    Task<List<UserDto>> GetUsersAsync(int page = 1, int pageSize = 10);
}
