using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PetSitterConnect.Api.Data;
using PetSitterConnect.Core.Models;

namespace PetSitterConnect.Api.Services;

public class PetCareRequestService : IPetCareRequestService
{
    private readonly ApplicationDbContext _context;
    private readonly IMapper _mapper;

    public PetCareRequestService(ApplicationDbContext context, IMapper mapper)
    {
        _context = context;
        _mapper = mapper;
    }

    public async Task<List<PetCareRequestDto>> GetRequestsAsync(int page = 1, int pageSize = 10)
    {
        var requests = await _context.PetCareRequests
            .Include(r => r.Pet)
            .Include(r => r.Owner)
            .Where(r => r.Status == RequestStatus.Open)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return _mapper.Map<List<PetCareRequestDto>>(requests);
    }

    public async Task<List<PetCareRequestDto>> GetUserRequestsAsync(string userId)
    {
        var requests = await _context.PetCareRequests
            .Include(r => r.Pet)
            .Where(r => r.OwnerId == userId)
            .ToListAsync();

        return _mapper.Map<List<PetCareRequestDto>>(requests);
    }

    public async Task<PetCareRequestDto?> GetRequestByIdAsync(string requestId)
    {
        var request = await _context.PetCareRequests
            .Include(r => r.Pet)
            .Include(r => r.Owner)
            .FirstOrDefaultAsync(r => r.Id == requestId);

        return request != null ? _mapper.Map<PetCareRequestDto>(request) : null;
    }

    public async Task<PetCareRequestDto?> CreateRequestAsync(string userId, CreatePetCareRequestDto createRequestDto)
    {
        var request = new PetCareRequest
        {
            Title = createRequestDto.Title,
            Description = createRequestDto.Description,
            StartDate = createRequestDto.StartDate,
            EndDate = createRequestDto.EndDate,
            Budget = createRequestDto.Budget,
            PetId = createRequestDto.PetId,
            OwnerId = userId
        };

        _context.PetCareRequests.Add(request);
        await _context.SaveChangesAsync();

        return _mapper.Map<PetCareRequestDto>(request);
    }

    public async Task<PetCareRequestDto?> UpdateRequestAsync(string requestId, UpdatePetCareRequestDto updateRequestDto)
    {
        var request = await _context.PetCareRequests.FindAsync(requestId);
        if (request == null)
            return null;

        request.Title = updateRequestDto.Title;
        request.Description = updateRequestDto.Description;
        request.StartDate = updateRequestDto.StartDate;
        request.EndDate = updateRequestDto.EndDate;
        request.Budget = updateRequestDto.Budget;
        request.UpdatedAt = DateTime.UtcNow;

        await _context.SaveChangesAsync();

        return _mapper.Map<PetCareRequestDto>(request);
    }

    public async Task<bool> DeleteRequestAsync(string requestId)
    {
        var request = await _context.PetCareRequests.FindAsync(requestId);
        if (request == null)
            return false;

        _context.PetCareRequests.Remove(request);
        await _context.SaveChangesAsync();

        return true;
    }
}
