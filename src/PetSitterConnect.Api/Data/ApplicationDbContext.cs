using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using PetSitterConnect.Core.Models;

namespace PetSitterConnect.Api.Data;

public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
    {
    }

    public DbSet<User> AppUsers { get; set; }
    public DbSet<Pet> Pets { get; set; }
    public DbSet<SitterProfile> SitterProfiles { get; set; }
    public DbSet<PetCareRequest> PetCareRequests { get; set; }
    public DbSet<Booking> Bookings { get; set; }
    public DbSet<ChatMessage> ChatMessages { get; set; }
    public DbSet<Review> Reviews { get; set; }
    public DbSet<Payment> Payments { get; set; }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        // User Configuration
        builder.Entity<User>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Email).IsRequired().HasMaxLength(256);
            entity.Property(e => e.FirstName).IsRequired().HasMaxLength(100);
            entity.Property(e => e.LastName).IsRequired().HasMaxLength(100);
            entity.Property(e => e.PhoneNumber).HasMaxLength(20);
            entity.Property(e => e.Address).HasMaxLength(500);
            entity.Property(e => e.City).HasMaxLength(100);
            entity.Property(e => e.State).HasMaxLength(50);
            entity.Property(e => e.ZipCode).HasMaxLength(10);

            entity.HasIndex(e => e.Email).IsUnique();
        });

        // Pet Configuration
        builder.Entity<Pet>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Breed).HasMaxLength(100);
            entity.Property(e => e.Size).HasMaxLength(50);
            entity.Property(e => e.Color).HasMaxLength(50);

            entity.HasOne(e => e.Owner)
                  .WithMany(e => e.Pets)
                  .HasForeignKey(e => e.OwnerId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // SitterProfile Configuration
        builder.Entity<SitterProfile>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.HourlyRate).HasColumnType("decimal(18,2)");
            entity.Property(e => e.DailyRate).HasColumnType("decimal(18,2)");

            entity.HasOne(e => e.User)
                  .WithOne(e => e.SitterProfile)
                  .HasForeignKey<SitterProfile>(e => e.UserId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // PetCareRequest Configuration
        builder.Entity<PetCareRequest>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Title).IsRequired().HasMaxLength(200);
            entity.Property(e => e.Budget).HasColumnType("decimal(18,2)");

            entity.HasOne(e => e.Owner)
                  .WithMany(e => e.PetCareRequests)
                  .HasForeignKey(e => e.OwnerId)
                  .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(e => e.Pet)
                  .WithMany(e => e.PetCareRequests)
                  .HasForeignKey(e => e.PetId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // Booking Configuration
        builder.Entity<Booking>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.TotalAmount).HasColumnType("decimal(18,2)");

            entity.HasOne(e => e.PetCareRequest)
                  .WithMany(e => e.Bookings)
                  .HasForeignKey(e => e.PetCareRequestId)
                  .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.Sitter)
                  .WithMany(e => e.BookingsAsSitter)
                  .HasForeignKey(e => e.SitterId)
                  .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(e => e.Owner)
                  .WithMany(e => e.BookingsAsOwner)
                  .HasForeignKey(e => e.OwnerId)
                  .OnDelete(DeleteBehavior.Restrict);
        });

        // ChatMessage Configuration
        builder.Entity<ChatMessage>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Message).IsRequired();

            entity.HasOne(e => e.Sender)
                  .WithMany()
                  .HasForeignKey(e => e.SenderId)
                  .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(e => e.Booking)
                  .WithMany(e => e.ChatMessages)
                  .HasForeignKey(e => e.BookingId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // Review Configuration
        builder.Entity<Review>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Rating).IsRequired();

            entity.HasOne(e => e.Reviewer)
                  .WithMany(e => e.ReviewsGiven)
                  .HasForeignKey(e => e.ReviewerId)
                  .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(e => e.Reviewee)
                  .WithMany(e => e.ReviewsReceived)
                  .HasForeignKey(e => e.RevieweeId)
                  .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(e => e.Booking)
                  .WithMany(e => e.Reviews)
                  .HasForeignKey(e => e.BookingId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // Payment Configuration
        builder.Entity<Payment>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Amount).HasColumnType("decimal(18,2)");

            entity.HasOne(e => e.Booking)
                  .WithOne(e => e.Payment)
                  .HasForeignKey<Payment>(e => e.BookingId)
                  .OnDelete(DeleteBehavior.Cascade);
        });
    }
}
