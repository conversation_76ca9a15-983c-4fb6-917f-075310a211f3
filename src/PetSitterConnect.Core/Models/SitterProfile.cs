using System.ComponentModel.DataAnnotations;

namespace PetSitterConnect.Core.Models;

public class SitterProfile
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required]
    public string UserId { get; set; } = string.Empty;
    
    public int YearsOfExperience { get; set; }
    
    public string? Experience { get; set; }
    
    public string? Services { get; set; }
    
    public decimal HourlyRate { get; set; }
    
    public decimal DailyRate { get; set; }
    
    public bool IsAvailable { get; set; } = true;
    
    public string? Availability { get; set; }
    
    public int MaxPets { get; set; } = 1;
    
    public string? PreferredPetTypes { get; set; }
    
    public string? PreferredPetSizes { get; set; }
    
    public bool HasOwnPets { get; set; }
    
    public bool HasYard { get; set; }
    
    public bool CanHostOvernight { get; set; }
    
    public bool CanTravelToOwner { get; set; }
    
    public string? EmergencyContact { get; set; }
    
    public string? References { get; set; }
    
    public bool IsVerified { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    // Navigation properties
    public virtual User User { get; set; } = null!;
}
