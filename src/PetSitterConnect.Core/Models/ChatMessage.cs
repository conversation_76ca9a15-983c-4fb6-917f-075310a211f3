using System.ComponentModel.DataAnnotations;

namespace PetSitterConnect.Core.Models;

public class ChatMessage
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required]
    public string Message { get; set; } = string.Empty;
    
    public DateTime SentAt { get; set; } = DateTime.UtcNow;
    
    public bool IsRead { get; set; }
    
    public MessageType Type { get; set; } = MessageType.Text;
    
    public string? AttachmentUrl { get; set; }
    
    // Foreign keys
    [Required]
    public string SenderId { get; set; } = string.Empty;
    
    [Required]
    public string BookingId { get; set; } = string.Empty;
    
    // Navigation properties
    public virtual User Sender { get; set; } = null!;
    public virtual Booking Booking { get; set; } = null!;
}

public enum MessageType
{
    Text = 1,
    Image = 2,
    File = 3
}
