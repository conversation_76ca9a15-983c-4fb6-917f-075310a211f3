using System.ComponentModel.DataAnnotations;

namespace PetSitterConnect.Core.Models;

public class Review
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required]
    [Range(1, 5)]
    public int Rating { get; set; }
    
    public string? Comment { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    // Foreign keys
    [Required]
    public string ReviewerId { get; set; } = string.Empty;
    
    [Required]
    public string RevieweeId { get; set; } = string.Empty;
    
    [Required]
    public string BookingId { get; set; } = string.Empty;
    
    // Navigation properties
    public virtual User Reviewer { get; set; } = null!;
    public virtual User Reviewee { get; set; } = null!;
    public virtual Booking Booking { get; set; } = null!;
}
