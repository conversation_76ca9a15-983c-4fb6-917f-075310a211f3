using System.ComponentModel.DataAnnotations;

namespace PetSitterConnect.Core.Models;

public class Payment
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required]
    public decimal Amount { get; set; }
    
    public PaymentStatus Status { get; set; } = PaymentStatus.Pending;
    
    public PaymentMethod Method { get; set; }
    
    public string? TransactionId { get; set; }
    
    public string? StripePaymentIntentId { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? ProcessedAt { get; set; }
    
    public DateTime? RefundedAt { get; set; }
    
    public string? RefundReason { get; set; }
    
    // Foreign key
    [Required]
    public string BookingId { get; set; } = string.Empty;
    
    // Navigation properties
    public virtual Booking Booking { get; set; } = null!;
}

public enum PaymentStatus
{
    Pending = 1,
    Processing = 2,
    Completed = 3,
    Failed = 4,
    Refunded = 5,
    Cancelled = 6
}

public enum PaymentMethod
{
    CreditCard = 1,
    DebitCard = 2,
    PayPal = 3,
    BankTransfer = 4
}
