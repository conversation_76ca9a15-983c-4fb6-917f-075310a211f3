using System.ComponentModel.DataAnnotations;

namespace PetSitterConnect.Core.Models;

public class Booking
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required]
    public DateTime StartDate { get; set; }
    
    [Required]
    public DateTime EndDate { get; set; }
    
    [Required]
    public decimal TotalAmount { get; set; }
    
    public BookingStatus Status { get; set; } = BookingStatus.Pending;
    
    public string? Notes { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? ConfirmedAt { get; set; }
    
    public DateTime? CompletedAt { get; set; }
    
    // Foreign keys
    [Required]
    public string PetCareRequestId { get; set; } = string.Empty;
    
    [Required]
    public string SitterId { get; set; } = string.Empty;
    
    [Required]
    public string OwnerId { get; set; } = string.Empty;
    
    // Navigation properties
    public virtual PetCareRequest PetCareRequest { get; set; } = null!;
    public virtual User Sitter { get; set; } = null!;
    public virtual User Owner { get; set; } = null!;
    public virtual ICollection<ChatMessage> ChatMessages { get; set; } = new List<ChatMessage>();
    public virtual ICollection<Review> Reviews { get; set; } = new List<Review>();
    public virtual Payment? Payment { get; set; }
}

public enum BookingStatus
{
    Pending = 1,
    Confirmed = 2,
    InProgress = 3,
    Completed = 4,
    Cancelled = 5
}
