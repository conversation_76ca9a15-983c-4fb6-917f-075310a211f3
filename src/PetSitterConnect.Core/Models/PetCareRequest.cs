using System.ComponentModel.DataAnnotations;

namespace PetSitterConnect.Core.Models;

public class PetCareRequest
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required]
    public string Title { get; set; } = string.Empty;
    
    public string? Description { get; set; }
    
    [Required]
    public DateTime StartDate { get; set; }
    
    [Required]
    public DateTime EndDate { get; set; }
    
    [Required]
    public decimal Budget { get; set; }
    
    public string? SpecialInstructions { get; set; }
    
    public RequestStatus Status { get; set; } = RequestStatus.Open;
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    // Foreign keys
    [Required]
    public string OwnerId { get; set; } = string.Empty;
    
    [Required]
    public string PetId { get; set; } = string.Empty;
    
    // Navigation properties
    public virtual User Owner { get; set; } = null!;
    public virtual Pet Pet { get; set; } = null!;
    public virtual ICollection<Booking> Bookings { get; set; } = new List<Booking>();
}

public enum RequestStatus
{
    Open = 1,
    InProgress = 2,
    Completed = 3,
    Cancelled = 4
}
