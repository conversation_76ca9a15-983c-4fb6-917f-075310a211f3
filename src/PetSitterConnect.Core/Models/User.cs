using System.ComponentModel.DataAnnotations;

namespace PetSitterConnect.Core.Models;

public class User
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;
    
    [Required]
    public string FirstName { get; set; } = string.Empty;
    
    [Required]
    public string LastName { get; set; } = string.Empty;
    
    [Phone]
    public string? PhoneNumber { get; set; }
    
    public string? ProfileImageUrl { get; set; }
    
    public string? Bio { get; set; }
    
    public string? Address { get; set; }
    
    public string? City { get; set; }
    
    public string? State { get; set; }
    
    public string? ZipCode { get; set; }
    
    public UserType UserType { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    public bool IsActive { get; set; } = true;
    
    // Navigation properties
    public virtual ICollection<PetCareRequest> PetCareRequests { get; set; } = new List<PetCareRequest>();
    public virtual ICollection<Booking> BookingsAsSitter { get; set; } = new List<Booking>();
    public virtual ICollection<Booking> BookingsAsOwner { get; set; } = new List<Booking>();
    public virtual ICollection<Review> ReviewsGiven { get; set; } = new List<Review>();
    public virtual ICollection<Review> ReviewsReceived { get; set; } = new List<Review>();
    public virtual ICollection<Pet> Pets { get; set; } = new List<Pet>();
    public virtual SitterProfile? SitterProfile { get; set; }
}

public enum UserType
{
    PetOwner = 1,
    PetSitter = 2,
    Both = 3
}
