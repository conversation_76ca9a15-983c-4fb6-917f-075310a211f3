using System.ComponentModel.DataAnnotations;

namespace PetSitterConnect.Core.Models;

public class Pet
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    [Required]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    public PetType Type { get; set; }
    
    public string? Breed { get; set; }
    
    public int Age { get; set; }
    
    public string? Size { get; set; }
    
    public string? Color { get; set; }
    
    public string? Description { get; set; }
    
    public string? SpecialNeeds { get; set; }
    
    public string? MedicalConditions { get; set; }
    
    public string? ImageUrl { get; set; }
    
    public bool IsVaccinated { get; set; }
    
    public bool IsNeutered { get; set; }
    
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    
    // Foreign key
    [Required]
    public string OwnerId { get; set; } = string.Empty;
    
    // Navigation properties
    public virtual User Owner { get; set; } = null!;
    public virtual ICollection<PetCareRequest> PetCareRequests { get; set; } = new List<PetCareRequest>();
}

public enum PetType
{
    Dog = 1,
    Cat = 2,
    <PERSON> = 3,
    <PERSON> = 4,
    <PERSON> = 5,
    <PERSON><PERSON> = 6,
    Other = 7
}
