{"version": 2, "dgSpecHash": "tBCqScz3uNU=", "success": true, "projectFilePath": "/Users/<USER>/Documents/PetCare /src/PetSitterConnect.Core/PetSitterConnect.Core.csproj", "expectedPackageFiles": ["/Users/<USER>/.nuget/packages/newtonsoft.json/13.0.3/newtonsoft.json.13.0.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.componentmodel.annotations/5.0.0/system.componentmodel.annotations.5.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.netcore.app.ref/8.0.17/microsoft.netcore.app.ref.8.0.17.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/8.0.17/microsoft.aspnetcore.app.ref.8.0.17.nupkg.sha512"], "logs": []}