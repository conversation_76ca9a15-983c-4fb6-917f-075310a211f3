{"format": 1, "restore": {"/Users/<USER>/Documents/PetCare /src/PetSitterConnect.Core/PetSitterConnect.Core.csproj": {}}, "projects": {"/Users/<USER>/Documents/PetCare /src/PetSitterConnect.Core/PetSitterConnect.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/PetCare /src/PetSitterConnect.Core/PetSitterConnect.Core.csproj", "projectName": "PetSitterConnect.Core", "projectPath": "/Users/<USER>/Documents/PetCare /src/PetSitterConnect.Core/PetSitterConnect.Core.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/PetCare /src/PetSitterConnect.Core/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"/usr/local/share/dotnet/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.17, 8.0.17]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.301/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"iossimulator-arm64": {"#import": []}}}}}