using PetSitterConnect.Core.Models;

namespace PetSitterConnect.Core.DTOs;

public class PetDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public PetType Type { get; set; }
    public string? Breed { get; set; }
    public int Age { get; set; }
    public string? Size { get; set; }
    public string? Color { get; set; }
    public string? Description { get; set; }
    public string? SpecialNeeds { get; set; }
    public string? MedicalConditions { get; set; }
    public string? ImageUrl { get; set; }
    public bool IsVaccinated { get; set; }
    public bool IsNeutered { get; set; }
    public string OwnerId { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
}

public class CreatePetDto
{
    public string Name { get; set; } = string.Empty;
    public PetType Type { get; set; }
    public string? Breed { get; set; }
    public int Age { get; set; }
    public string? Size { get; set; }
    public string? Color { get; set; }
    public string? Description { get; set; }
    public string? SpecialNeeds { get; set; }
    public string? MedicalConditions { get; set; }
    public bool IsVaccinated { get; set; }
    public bool IsNeutered { get; set; }
}

public class UpdatePetDto
{
    public string Name { get; set; } = string.Empty;
    public string? Breed { get; set; }
    public int Age { get; set; }
    public string? Size { get; set; }
    public string? Color { get; set; }
    public string? Description { get; set; }
    public string? SpecialNeeds { get; set; }
    public string? MedicalConditions { get; set; }
    public bool IsVaccinated { get; set; }
    public bool IsNeutered { get; set; }
}
