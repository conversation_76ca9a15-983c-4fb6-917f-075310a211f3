namespace PetSitterConnect.Core.DTOs;

public class SitterProfileDto
{
    public string Id { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public int YearsOfExperience { get; set; }
    public string? Experience { get; set; }
    public string? Services { get; set; }
    public decimal HourlyRate { get; set; }
    public decimal DailyRate { get; set; }
    public bool IsAvailable { get; set; }
    public string? Availability { get; set; }
    public int MaxPets { get; set; }
    public string? PreferredPetTypes { get; set; }
    public string? PreferredPetSizes { get; set; }
    public bool HasOwnPets { get; set; }
    public bool HasYard { get; set; }
    public bool CanHostOvernight { get; set; }
    public bool CanTravelToOwner { get; set; }
    public string? EmergencyContact { get; set; }
    public string? References { get; set; }
    public bool IsVerified { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class CreateSitterProfileDto
{
    public int YearsOfExperience { get; set; }
    public string? Experience { get; set; }
    public string? Services { get; set; }
    public decimal HourlyRate { get; set; }
    public decimal DailyRate { get; set; }
    public string? Availability { get; set; }
    public int MaxPets { get; set; } = 1;
    public string? PreferredPetTypes { get; set; }
    public string? PreferredPetSizes { get; set; }
    public bool HasOwnPets { get; set; }
    public bool HasYard { get; set; }
    public bool CanHostOvernight { get; set; }
    public bool CanTravelToOwner { get; set; }
    public string? EmergencyContact { get; set; }
    public string? References { get; set; }
}

public class UpdateSitterProfileDto
{
    public int YearsOfExperience { get; set; }
    public string? Experience { get; set; }
    public string? Services { get; set; }
    public decimal HourlyRate { get; set; }
    public decimal DailyRate { get; set; }
    public bool IsAvailable { get; set; }
    public string? Availability { get; set; }
    public int MaxPets { get; set; }
    public string? PreferredPetTypes { get; set; }
    public string? PreferredPetSizes { get; set; }
    public bool HasOwnPets { get; set; }
    public bool HasYard { get; set; }
    public bool CanHostOvernight { get; set; }
    public bool CanTravelToOwner { get; set; }
    public string? EmergencyContact { get; set; }
    public string? References { get; set; }
}
