using PetSitterConnect.Core.Models;
using System.ComponentModel.DataAnnotations;

namespace PetSitterConnect.Core.DTOs;

public class ChatMessageDto
{
    public string Id { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime SentAt { get; set; }
    public bool IsRead { get; set; }
    public MessageType Type { get; set; }
    public string? AttachmentUrl { get; set; }
    
    public string SenderId { get; set; } = string.Empty;
    public string BookingId { get; set; } = string.Empty;
    
    // Navigation properties
    public UserDto? Sender { get; set; }
}

public class CreateChatMessageDto
{
    [Required]
    public string Message { get; set; } = string.Empty;
    
    public MessageType Type { get; set; } = MessageType.Text;
    
    public string? AttachmentUrl { get; set; }
    
    [Required]
    public string BookingId { get; set; } = string.Empty;
}

public class ReviewDto
{
    public string Id { get; set; } = string.Empty;
    public int Rating { get; set; }
    public string? Comment { get; set; }
    public DateTime CreatedAt { get; set; }
    
    public string ReviewerId { get; set; } = string.Empty;
    public string RevieweeId { get; set; } = string.Empty;
    public string BookingId { get; set; } = string.Empty;
    
    // Navigation properties
    public UserDto? Reviewer { get; set; }
    public UserDto? Reviewee { get; set; }
}

public class CreateReviewDto
{
    [Required]
    [Range(1, 5)]
    public int Rating { get; set; }
    
    public string? Comment { get; set; }
    
    [Required]
    public string RevieweeId { get; set; } = string.Empty;
    
    [Required]
    public string BookingId { get; set; } = string.Empty;
}

public class PaymentDto
{
    public string Id { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public PaymentStatus Status { get; set; }
    public PaymentMethod Method { get; set; }
    public string? TransactionId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? ProcessedAt { get; set; }
    
    public string BookingId { get; set; } = string.Empty;
}

public class CreatePaymentDto
{
    [Required]
    [Range(0.01, double.MaxValue)]
    public decimal Amount { get; set; }
    
    public PaymentMethod Method { get; set; }
    
    [Required]
    public string BookingId { get; set; } = string.Empty;
}
