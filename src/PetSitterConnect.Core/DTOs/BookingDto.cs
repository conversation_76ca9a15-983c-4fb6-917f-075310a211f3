using PetSitterConnect.Core.Models;
using System.ComponentModel.DataAnnotations;

namespace PetSitterConnect.Core.DTOs;

public class BookingDto
{
    public string Id { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public decimal TotalAmount { get; set; }
    public BookingStatus Status { get; set; }
    public string? Notes { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? ConfirmedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    
    public string PetCareRequestId { get; set; } = string.Empty;
    public string SitterId { get; set; } = string.Empty;
    public string OwnerId { get; set; } = string.Empty;
    
    // Navigation properties
    public PetCareRequestDto? PetCareRequest { get; set; }
    public UserDto? Sitter { get; set; }
    public UserDto? Owner { get; set; }
}

public class CreateBookingDto
{
    [Required]
    public DateTime StartDate { get; set; }
    
    [Required]
    public DateTime EndDate { get; set; }
    
    [Required]
    [Range(0.01, double.MaxValue)]
    public decimal TotalAmount { get; set; }
    
    public string? Notes { get; set; }
    
    [Required]
    public string PetCareRequestId { get; set; } = string.Empty;
}

public class UpdateBookingDto
{
    public BookingStatus Status { get; set; }
    public string? Notes { get; set; }
}
