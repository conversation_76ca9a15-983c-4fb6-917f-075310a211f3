using PetSitterConnect.Core.Models;
using System.ComponentModel.DataAnnotations;

namespace PetSitterConnect.Core.DTOs;

public class PetCareRequestDto
{
    public string Id { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string? Description { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public decimal Budget { get; set; }
    public string? SpecialInstructions { get; set; }
    public RequestStatus Status { get; set; }
    public DateTime CreatedAt { get; set; }
    public string OwnerId { get; set; } = string.Empty;
    public string PetId { get; set; } = string.Empty;
    
    // Navigation properties
    public UserDto? Owner { get; set; }
    public PetDto? Pet { get; set; }
}

public class CreatePetCareRequestDto
{
    [Required]
    [MaxLength(200)]
    public string Title { get; set; } = string.Empty;
    
    public string? Description { get; set; }
    
    [Required]
    public DateTime StartDate { get; set; }
    
    [Required]
    public DateTime EndDate { get; set; }
    
    [Required]
    [Range(0.01, double.MaxValue, ErrorMessage = "Budget must be greater than 0")]
    public decimal Budget { get; set; }
    
    public string? SpecialInstructions { get; set; }
    
    [Required]
    public string PetId { get; set; } = string.Empty;
}

public class UpdatePetCareRequestDto
{
    [Required]
    [MaxLength(200)]
    public string Title { get; set; } = string.Empty;
    
    public string? Description { get; set; }
    
    [Required]
    public DateTime StartDate { get; set; }
    
    [Required]
    public DateTime EndDate { get; set; }
    
    [Required]
    [Range(0.01, double.MaxValue, ErrorMessage = "Budget must be greater than 0")]
    public decimal Budget { get; set; }
    
    public string? SpecialInstructions { get; set; }
}
