using Microsoft.Extensions.Logging;
using PetSitterConnect.Services;
using PetSitterConnect.ViewModels;
using PetSitterConnect.Views;
using CommunityToolkit.Maui;

namespace PetSitterConnect;

public static class MauiProgram
{
    public static MauiApp CreateMauiApp()
    {
        var builder = MauiApp.CreateBuilder();
        builder
            .UseMauiApp<App>()
            .UseMauiCommunityToolkit()
            .ConfigureFonts(fonts =>
            {
                fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
            });

        // Register Services
        builder.Services.AddSingleton<IApiService, ApiService>();
        builder.Services.AddSingleton<IAuthService, AuthService>();
        builder.Services.AddSingleton<INavigationService, NavigationService>();
        builder.Services.AddSingleton<IDialogService, DialogService>();
        builder.Services.AddSingleton<ILocalStorageService, LocalStorageService>();

        // Register ViewModels
        builder.Services.AddTransient<AppShellViewModel>();
        builder.Services.AddTransient<LoginViewModel>();
        builder.Services.AddTransient<RegisterViewModel>();
        builder.Services.AddTransient<MainViewModel>();
        builder.Services.AddTransient<ProfileViewModel>();
        builder.Services.AddTransient<PetListViewModel>();
        builder.Services.AddTransient<PetDetailViewModel>();
        builder.Services.AddTransient<RequestListViewModel>();
        builder.Services.AddTransient<CreateRequestViewModel>();
        builder.Services.AddTransient<BookingListViewModel>();
        builder.Services.AddTransient<ChatViewModel>();

        // Register Views
        builder.Services.AddTransient<AppShell>();
        builder.Services.AddTransient<LoginPage>();
        builder.Services.AddTransient<RegisterPage>();
        builder.Services.AddTransient<MainPage>();
        builder.Services.AddTransient<ProfilePage>();
        builder.Services.AddTransient<PetListPage>();
        builder.Services.AddTransient<PetDetailPage>();
        builder.Services.AddTransient<RequestListPage>();
        builder.Services.AddTransient<CreateRequestPage>();
        builder.Services.AddTransient<BookingListPage>();
        builder.Services.AddTransient<ChatPage>();
        builder.Services.AddTransient<SettingsPage>();
        builder.Services.AddTransient<HelpPage>();

        // Configure HTTP Client
        builder.Services.AddHttpClient("PetSitterApi", client =>
        {
            client.BaseAddress = new Uri("https://localhost:7001/api/"); // Update with your API URL
            client.DefaultRequestHeaders.Add("Accept", "application/json");
        });

#if DEBUG
        builder.Logging.AddDebug();
#endif

        return builder.Build();
    }
}
