<?xml version="1.0" encoding="UTF-8" ?>
<Shell
    x:Class="PetSitterConnect.AppShell"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:PetSitterConnect.Views"
    Title="PetSitter Connect">

    <Shell.FlyoutBehavior>
        <FlyoutBehavior>Flyout</FlyoutBehavior>
    </Shell.FlyoutBehavior>

    <!-- Main Tabs -->
    <TabBar>
        <ShellContent
            Title="Home"
            Icon="home.png"
            ContentTemplate="{DataTemplate local:MainPage}"
            Route="main" />

        <ShellContent
            Title="Requests"
            Icon="list.png"
            ContentTemplate="{DataTemplate local:RequestListPage}"
            Route="requests" />

        <ShellContent
            Title="Bookings"
            Icon="calendar.png"
            ContentTemplate="{DataTemplate local:BookingListPage}"
            Route="bookings" />

        <ShellContent
            Title="Pets"
            Icon="pets.png"
            ContentTemplate="{DataTemplate local:PetListPage}"
            Route="pets" />

        <ShellContent
            Title="Profile"
            Icon="person.png"
            ContentTemplate="{DataTemplate local:ProfilePage}"
            Route="profile" />
    </TabBar>

    <!-- Flyout Menu -->
    <FlyoutItem Title="Settings" Icon="settings.png">
        <ShellContent ContentTemplate="{DataTemplate local:SettingsPage}" />
    </FlyoutItem>

    <FlyoutItem Title="Help" Icon="help.png">
        <ShellContent ContentTemplate="{DataTemplate local:HelpPage}" />
    </FlyoutItem>

    <MenuItem Text="Logout" 
              IconImageSource="logout.png"
              Command="{Binding LogoutCommand}" />

</Shell>
