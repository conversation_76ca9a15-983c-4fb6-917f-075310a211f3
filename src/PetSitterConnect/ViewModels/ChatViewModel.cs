using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using PetSitterConnect.Core.DTOs;
using PetSitterConnect.Services;
using System.Collections.ObjectModel;

namespace PetSitterConnect.ViewModels;

public partial class ChatViewModel : BaseViewModel, IQueryAttributable
{
    private readonly IApiService _apiService;
    private readonly IAuthService _authService;

    [ObservableProperty]
    private string _messageText = string.Empty;

    [ObservableProperty]
    private ObservableCollection<ChatMessageDto> _messages = new();

    [ObservableProperty]
    private string _bookingId = string.Empty;

    public ChatViewModel(IApiService apiService, IAuthService authService, INavigationService navigationService, IDialogService dialogService)
        : base(navigationService, dialogService)
    {
        _apiService = apiService;
        _authService = authService;
        Title = "Chat";
    }

    public void ApplyQueryAttributes(IDictionary<string, object> query)
    {
        if (query.ContainsKey("bookingId"))
        {
            BookingId = query["bookingId"].ToString() ?? string.Empty;
            LoadMessages();
        }
    }

    private async void LoadMessages()
    {
        if (string.IsNullOrEmpty(BookingId))
            return;

        await ExecuteAsync(async () =>
        {
            // TODO: Implement when chat API is ready
            // var messages = await _apiService.GetChatMessagesAsync(BookingId);
            // Messages.Clear();
            // foreach (var message in messages)
            // {
            //     Messages.Add(message);
            // }
            await Task.CompletedTask;
        });
    }

    [RelayCommand]
    private async Task SendMessageAsync()
    {
        if (string.IsNullOrWhiteSpace(MessageText) || string.IsNullOrEmpty(BookingId))
            return;

        await ExecuteAsync(async () =>
        {
            // TODO: Implement when chat API is ready
            // var createMessageDto = new CreateChatMessageDto
            // {
            //     Message = MessageText,
            //     BookingId = BookingId
            // };

            // var message = await _apiService.SendChatMessageAsync(createMessageDto);
            // if (message != null)
            // {
            //     Messages.Add(message);
            //     MessageText = string.Empty;
            // }

            MessageText = string.Empty;
            await Task.CompletedTask;
        });
    }
}
