using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using PetSitterConnect.Services;

namespace PetSitterConnect.ViewModels;

public partial class ChatViewModel : BaseViewModel
{
    [ObservableProperty]
    private string _messageText = string.Empty;

    public ChatViewModel(INavigationService navigationService, IDialogService dialogService)
        : base(navigationService, dialogService)
    {
        Title = "Chat";
    }

    [RelayCommand]
    private async Task SendMessageAsync()
    {
        if (string.IsNullOrWhiteSpace(MessageText))
            return;

        // TODO: Implement send message functionality
        MessageText = string.Empty;
        await Task.CompletedTask;
    }
}
