using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using PetSitterConnect.Core.DTOs;
using PetSitterConnect.Services;

namespace PetSitterConnect.ViewModels;

public partial class ProfileViewModel : BaseViewModel
{
    private readonly IAuthService _authService;

    [ObservableProperty]
    private UserDto? _currentUser;

    [ObservableProperty]
    private string _firstName = string.Empty;

    [ObservableProperty]
    private string _lastName = string.Empty;

    [ObservableProperty]
    private string _email = string.Empty;

    [ObservableProperty]
    private string _phoneNumber = string.Empty;

    [ObservableProperty]
    private string _bio = string.Empty;

    public ProfileViewModel(IAuthService authService, INavigationService navigationService, IDialogService dialogService)
        : base(navigationService, dialogService)
    {
        _authService = authService;
        Title = "Profile";
        LoadUserProfile();
    }

    private async void LoadUserProfile()
    {
        await ExecuteAsync(async () =>
        {
            CurrentUser = await _authService.GetCurrentUserAsync();
            if (CurrentUser != null)
            {
                FirstName = CurrentUser.FirstName;
                LastName = CurrentUser.LastName;
                Email = CurrentUser.Email;
                PhoneNumber = CurrentUser.PhoneNumber ?? string.Empty;
                Bio = CurrentUser.Bio ?? string.Empty;
            }
        });
    }

    [RelayCommand]
    private async Task SaveProfileAsync()
    {
        await ExecuteAsync(async () =>
        {
            // TODO: Implement profile update API call
            await _dialogService.ShowAlertAsync("Success", "Profile updated successfully!");
        });
    }

    [RelayCommand]
    private async Task LogoutAsync()
    {
        var confirm = await _dialogService.ShowConfirmAsync("Logout", "Are you sure you want to logout?");
        if (confirm)
        {
            await _authService.LogoutAsync();
            Application.Current.MainPage = new NavigationPage(new Views.LoginPage(
                ServiceHelper.GetService<LoginViewModel>()));
        }
    }
}
