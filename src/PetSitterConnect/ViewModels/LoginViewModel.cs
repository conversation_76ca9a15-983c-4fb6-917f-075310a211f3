using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using PetSitterConnect.Services;
using System.ComponentModel.DataAnnotations;

namespace PetSitterConnect.ViewModels;

public partial class LoginViewModel : BaseViewModel
{
    private readonly IAuthService _authService;

    [ObservableProperty]
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Please enter a valid email address")]
    private string _email = string.Empty;

    [ObservableProperty]
    [Required(ErrorMessage = "Password is required")]
    [MinLength(6, ErrorMessage = "Password must be at least 6 characters")]
    private string _password = string.Empty;

    [ObservableProperty]
    private bool _rememberMe;

    [ObservableProperty]
    private string _emailError = string.Empty;

    [ObservableProperty]
    private string _passwordError = string.Empty;

    public LoginViewModel(IAuthService authService, INavigationService navigationService, IDialogService dialogService)
        : base(navigationService, dialogService)
    {
        _authService = authService;
        Title = "Login";
    }

    [RelayCommand]
    private async Task LoginAsync()
    {
        if (!ValidateForm())
            return;

        await ExecuteAsync(async () =>
        {
            var success = await _authService.LoginAsync(Email, Password);
            
            if (success)
            {
                // Navigate to main app
                Application.Current.MainPage = new AppShell();
            }
            else
            {
                await _dialogService.ShowAlertAsync("Login Failed", "Invalid email or password. Please try again.");
            }
        });
    }

    [RelayCommand]
    private async Task NavigateToRegisterAsync()
    {
        await _navigationService.NavigateToAsync("//register");
    }

    [RelayCommand]
    private async Task ForgotPasswordAsync()
    {
        var email = await _dialogService.ShowPromptAsync(
            "Forgot Password", 
            "Enter your email address to reset your password:",
            "Send",
            "Cancel",
            "Email address");

        if (!string.IsNullOrEmpty(email))
        {
            // TODO: Implement forgot password functionality
            await _dialogService.ShowAlertAsync("Password Reset", "Password reset instructions have been sent to your email.");
        }
    }

    private bool ValidateForm()
    {
        EmailError = string.Empty;
        PasswordError = string.Empty;

        var isValid = true;

        if (string.IsNullOrWhiteSpace(Email))
        {
            EmailError = "Email is required";
            isValid = false;
        }
        else if (!IsValidEmail(Email))
        {
            EmailError = "Please enter a valid email address";
            isValid = false;
        }

        if (string.IsNullOrWhiteSpace(Password))
        {
            PasswordError = "Password is required";
            isValid = false;
        }
        else if (Password.Length < 6)
        {
            PasswordError = "Password must be at least 6 characters";
            isValid = false;
        }

        return isValid;
    }

    private bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }
}
