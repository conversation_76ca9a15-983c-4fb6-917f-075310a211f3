using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using PetSitterConnect.Core.DTOs;
using PetSitterConnect.Services;
using System.Collections.ObjectModel;

namespace PetSitterConnect.ViewModels;

public partial class BookingListViewModel : BaseViewModel
{
    private readonly IApiService _apiService;

    [ObservableProperty]
    private ObservableCollection<BookingDto> _bookings = new();

    public BookingListViewModel(IApiService apiService, INavigationService navigationService, IDialogService dialogService)
        : base(navigationService, dialogService)
    {
        _apiService = apiService;
        Title = "My Bookings";
        LoadBookings();
    }

    private async void LoadBookings()
    {
        await ExecuteAsync(async () =>
        {
            // TODO: Implement when booking API is ready
            // var bookings = await _apiService.GetUserBookingsAsync();
            // Bookings.Clear();
            // foreach (var booking in bookings)
            // {
            //     Bookings.Add(booking);
            // }
            await Task.CompletedTask;
        });
    }

    [RelayCommand]
    private async Task ViewBookingAsync(BookingDto booking)
    {
        var parameters = new Dictionary<string, object>
        {
            { "booking", booking }
        };
        await _navigationService.NavigateToAsync("bookingdetail", parameters);
    }

    [RelayCommand]
    private async Task RefreshAsync()
    {
        LoadBookings();
    }
}
