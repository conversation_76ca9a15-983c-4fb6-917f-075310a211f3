using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using PetSitterConnect.Services;

namespace PetSitterConnect.ViewModels;

public partial class BookingListViewModel : BaseViewModel
{
    public BookingListViewModel(INavigationService navigationService, IDialogService dialogService)
        : base(navigationService, dialogService)
    {
        Title = "My Bookings";
    }

    [RelayCommand]
    private async Task RefreshAsync()
    {
        // TODO: Implement refresh functionality
        await Task.CompletedTask;
    }
}
