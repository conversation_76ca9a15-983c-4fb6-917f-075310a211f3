using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using PetSitterConnect.Core.DTOs;
using PetSitterConnect.Core.Models;
using PetSitterConnect.Services;

namespace PetSitterConnect.ViewModels;

public partial class PetDetailViewModel : BaseViewModel, IQueryAttributable
{
    private readonly IApiService _apiService;

    [ObservableProperty]
    private PetDto? _pet;

    [ObservableProperty]
    private string _name = string.Empty;

    [ObservableProperty]
    private PetType _petType = PetType.Dog;

    [ObservableProperty]
    private string _breed = string.Empty;

    [ObservableProperty]
    private int _age;

    [ObservableProperty]
    private string _size = string.Empty;

    [ObservableProperty]
    private string _color = string.Empty;

    [ObservableProperty]
    private string _description = string.Empty;

    [ObservableProperty]
    private string _specialNeeds = string.Empty;

    [ObservableProperty]
    private string _medicalConditions = string.Empty;

    [ObservableProperty]
    private bool _isVaccinated;

    [ObservableProperty]
    private bool _isNeutered;

    [ObservableProperty]
    private bool _isEditMode;

    public PetDetailViewModel(IApiService apiService, INavigationService navigationService, IDialogService dialogService)
        : base(navigationService, dialogService)
    {
        _apiService = apiService;
        Title = "Pet Details";
    }

    public void ApplyQueryAttributes(IDictionary<string, object> query)
    {
        if (query.ContainsKey("pet"))
        {
            Pet = query["pet"] as PetDto;
            IsEditMode = Pet != null;
            LoadPetData();
        }
        else
        {
            IsEditMode = false;
            Title = "Add Pet";
        }
    }

    private void LoadPetData()
    {
        if (Pet != null)
        {
            Name = Pet.Name;
            PetType = Pet.Type;
            Breed = Pet.Breed ?? string.Empty;
            Age = Pet.Age;
            Size = Pet.Size ?? string.Empty;
            Color = Pet.Color ?? string.Empty;
            Description = Pet.Description ?? string.Empty;
            SpecialNeeds = Pet.SpecialNeeds ?? string.Empty;
            MedicalConditions = Pet.MedicalConditions ?? string.Empty;
            IsVaccinated = Pet.IsVaccinated;
            IsNeutered = Pet.IsNeutered;
        }
    }

    [RelayCommand]
    private async Task SavePetAsync()
    {
        await ExecuteAsync(async () =>
        {
            if (IsEditMode && Pet != null)
            {
                var updateDto = new UpdatePetDto
                {
                    Name = Name,
                    Breed = Breed,
                    Age = Age,
                    Size = Size,
                    Color = Color,
                    Description = Description,
                    SpecialNeeds = SpecialNeeds,
                    MedicalConditions = MedicalConditions,
                    IsVaccinated = IsVaccinated,
                    IsNeutered = IsNeutered
                };

                var updatedPet = await _apiService.UpdatePetAsync(Pet.Id, updateDto);
                if (updatedPet != null)
                {
                    await _dialogService.ShowAlertAsync("Success", "Pet updated successfully!");
                    await _navigationService.GoBackAsync();
                }
            }
            else
            {
                var createDto = new CreatePetDto
                {
                    Name = Name,
                    Type = PetType,
                    Breed = Breed,
                    Age = Age,
                    Size = Size,
                    Color = Color,
                    Description = Description,
                    SpecialNeeds = SpecialNeeds,
                    MedicalConditions = MedicalConditions,
                    IsVaccinated = IsVaccinated,
                    IsNeutered = IsNeutered
                };

                var newPet = await _apiService.CreatePetAsync(createDto);
                if (newPet != null)
                {
                    await _dialogService.ShowAlertAsync("Success", "Pet added successfully!");
                    await _navigationService.GoBackAsync();
                }
            }
        });
    }
}
