using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using PetSitterConnect.Services;

namespace PetSitterConnect.ViewModels;

public partial class BaseViewModel : ObservableObject
{
    protected readonly INavigationService _navigationService;
    protected readonly IDialogService _dialogService;

    [ObservableProperty]
    private bool _isBusy;

    [ObservableProperty]
    private string _title = string.Empty;

    [ObservableProperty]
    private bool _isRefreshing;

    public BaseViewModel(INavigationService navigationService, IDialogService dialogService)
    {
        _navigationService = navigationService;
        _dialogService = dialogService;
    }

    [RelayCommand]
    protected virtual async Task GoBackAsync()
    {
        await _navigationService.GoBackAsync();
    }

    protected async Task ExecuteAsync(Func<Task> operation, bool showLoading = true)
    {
        if (IsBusy)
            return;

        try
        {
            if (showLoading)
            {
                IsBusy = true;
                await _dialogService.ShowLoadingAsync();
            }

            await operation();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error: {ex.Message}");
            await _dialogService.ShowAlertAsync("Error", "An error occurred. Please try again.");
        }
        finally
        {
            if (showLoading)
            {
                IsBusy = false;
                await _dialogService.HideLoadingAsync();
            }
        }
    }

    protected async Task<T> ExecuteAsync<T>(Func<Task<T>> operation, bool showLoading = true)
    {
        if (IsBusy)
            return default(T);

        try
        {
            if (showLoading)
            {
                IsBusy = true;
                await _dialogService.ShowLoadingAsync();
            }

            return await operation();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error: {ex.Message}");
            await _dialogService.ShowAlertAsync("Error", "An error occurred. Please try again.");
            return default(T);
        }
        finally
        {
            if (showLoading)
            {
                IsBusy = false;
                await _dialogService.HideLoadingAsync();
            }
        }
    }
}
