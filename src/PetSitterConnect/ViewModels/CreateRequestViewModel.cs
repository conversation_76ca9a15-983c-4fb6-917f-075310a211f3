using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using PetSitterConnect.Core.DTOs;
using PetSitterConnect.Services;
using System.Collections.ObjectModel;

namespace PetSitterConnect.ViewModels;

public partial class CreateRequestViewModel : BaseViewModel
{
    private readonly IApiService _apiService;
    private readonly IAuthService _authService;

    [ObservableProperty]
    private string _title = string.Empty;

    [ObservableProperty]
    private string _description = string.Empty;

    [ObservableProperty]
    private DateTime _startDate = DateTime.Today.AddDays(1);

    [ObservableProperty]
    private DateTime _endDate = DateTime.Today.AddDays(2);

    [ObservableProperty]
    private decimal _budget;

    [ObservableProperty]
    private string _specialInstructions = string.Empty;

    [ObservableProperty]
    private ObservableCollection<PetDto> _userPets = new();

    [ObservableProperty]
    private PetDto? _selectedPet;

    [ObservableProperty]
    private bool _isHostingService = true;

    public DateTime MinDate => DateTime.Today;

    public CreateRequestViewModel(IApiService apiService, IAuthService authService, INavigationService navigationService, IDialogService dialogService)
        : base(navigationService, dialogService)
    {
        _apiService = apiService;
        _authService = authService;
        Title = "Create Request";
        LoadUserPets();
    }

    private async void LoadUserPets()
    {
        await ExecuteAsync(async () =>
        {
            var userId = _authService.GetCurrentUserId();
            if (!string.IsNullOrEmpty(userId))
            {
                var pets = await _apiService.GetUserPetsAsync(userId);
                UserPets.Clear();
                foreach (var pet in pets)
                {
                    UserPets.Add(pet);
                }

                if (UserPets.Any())
                {
                    SelectedPet = UserPets.First();
                }
            }
        }, false);
    }

    [RelayCommand]
    private async Task SaveRequestAsync()
    {
        if (!ValidateForm())
            return;

        await ExecuteAsync(async () =>
        {
            var createRequestDto = new CreatePetCareRequestDto
            {
                Title = Title,
                Description = Description,
                StartDate = StartDate,
                EndDate = EndDate,
                Budget = Budget,
                SpecialInstructions = SpecialInstructions,
                PetId = SelectedPet!.Id
            };

            var request = await _apiService.PostAsync<PetCareRequestDto>("petcarerequests", createRequestDto);
            if (request != null)
            {
                await _dialogService.ShowAlertAsync("Success", "Pet care request created successfully!");
                await _navigationService.GoBackAsync();
            }
            else
            {
                await _dialogService.ShowAlertAsync("Error", "Failed to create request. Please try again.");
            }
        });
    }

    private bool ValidateForm()
    {
        if (string.IsNullOrWhiteSpace(Title))
        {
            _dialogService.ShowAlertAsync("Validation Error", "Please enter a title for your request.");
            return false;
        }

        if (SelectedPet == null)
        {
            _dialogService.ShowAlertAsync("Validation Error", "Please select a pet for this request.");
            return false;
        }

        if (StartDate < DateTime.Today)
        {
            _dialogService.ShowAlertAsync("Validation Error", "Start date cannot be in the past.");
            return false;
        }

        if (EndDate <= StartDate)
        {
            _dialogService.ShowAlertAsync("Validation Error", "End date must be after start date.");
            return false;
        }

        if (Budget <= 0)
        {
            _dialogService.ShowAlertAsync("Validation Error", "Please enter a valid budget amount.");
            return false;
        }

        return true;
    }
}
