using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using PetSitterConnect.Services;

namespace PetSitterConnect.ViewModels;

public partial class CreateRequestViewModel : BaseViewModel
{
    [ObservableProperty]
    private string _title = string.Empty;

    [ObservableProperty]
    private string _description = string.Empty;

    [ObservableProperty]
    private DateTime _startDate = DateTime.Today;

    [ObservableProperty]
    private DateTime _endDate = DateTime.Today.AddDays(1);

    [ObservableProperty]
    private decimal _budget;

    public CreateRequestViewModel(INavigationService navigationService, IDialogService dialogService)
        : base(navigationService, dialogService)
    {
        Title = "Create Request";
    }

    [RelayCommand]
    private async Task SaveRequestAsync()
    {
        // TODO: Implement save request functionality
        await _dialogService.ShowAlertAsync("Success", "Request created successfully!");
        await _navigationService.GoBackAsync();
    }
}
