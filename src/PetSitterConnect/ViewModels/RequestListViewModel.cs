using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using PetSitterConnect.Core.DTOs;
using PetSitterConnect.Services;
using System.Collections.ObjectModel;

namespace PetSitterConnect.ViewModels;

public partial class RequestListViewModel : BaseViewModel
{
    private readonly IApiService _apiService;

    [ObservableProperty]
    private string _searchText = string.Empty;

    [ObservableProperty]
    private ObservableCollection<PetCareRequestDto> _requests = new();

    [ObservableProperty]
    private bool _showMyRequests = false;

    public RequestListViewModel(IApiService apiService, INavigationService navigationService, IDialogService dialogService)
        : base(navigationService, dialogService)
    {
        _apiService = apiService;
        Title = "Pet Care Requests";
        LoadRequests();
    }

    private async void LoadRequests()
    {
        await ExecuteAsync(async () =>
        {
            var requests = ShowMyRequests
                ? await _apiService.GetAsync<List<PetCareRequestDto>>("petcarerequests/my-requests")
                : await _apiService.GetAsync<List<PetCareRequestDto>>("petcarerequests");

            Requests.Clear();
            if (requests != null)
            {
                foreach (var request in requests)
                {
                    Requests.Add(request);
                }
            }
        });
    }

    [RelayCommand]
    private async Task CreateRequestAsync()
    {
        await _navigationService.NavigateToAsync("createrequest");
    }

    [RelayCommand]
    private async Task ViewRequestAsync(PetCareRequestDto request)
    {
        var parameters = new Dictionary<string, object>
        {
            { "request", request }
        };
        await _navigationService.NavigateToAsync("requestdetail", parameters);
    }

    [RelayCommand]
    private async Task SearchAsync()
    {
        // TODO: Implement search functionality with API
        await Task.CompletedTask;
    }

    [RelayCommand]
    private async Task RefreshAsync()
    {
        LoadRequests();
    }

    [RelayCommand]
    private async Task ToggleMyRequestsAsync()
    {
        ShowMyRequests = !ShowMyRequests;
        Title = ShowMyRequests ? "My Requests" : "Pet Care Requests";
        LoadRequests();
    }
}
