using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using PetSitterConnect.Services;

namespace PetSitterConnect.ViewModels;

public partial class RequestListViewModel : BaseViewModel
{
    [ObservableProperty]
    private string _searchText = string.Empty;

    public RequestListViewModel(INavigationService navigationService, IDialogService dialogService)
        : base(navigationService, dialogService)
    {
        Title = "Pet Care Requests";
    }

    [RelayCommand]
    private async Task CreateRequestAsync()
    {
        await _navigationService.NavigateToAsync("createrequest");
    }

    [RelayCommand]
    private async Task SearchAsync()
    {
        // TODO: Implement search functionality
        await Task.CompletedTask;
    }
}
