using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using PetSitterConnect.Core.DTOs;
using PetSitterConnect.Services;
using System.Collections.ObjectModel;

namespace PetSitterConnect.ViewModels;

public partial class PetListViewModel : BaseViewModel
{
    private readonly IApiService _apiService;
    private readonly IAuthService _authService;

    [ObservableProperty]
    private ObservableCollection<PetDto> _pets = new();

    public PetListViewModel(IApiService apiService, IAuthService authService, INavigationService navigationService, IDialogService dialogService)
        : base(navigationService, dialogService)
    {
        _apiService = apiService;
        _authService = authService;
        Title = "My Pets";
        LoadPets();
    }

    private async void LoadPets()
    {
        await ExecuteAsync(async () =>
        {
            var userId = _authService.GetCurrentUserId();
            if (!string.IsNullOrEmpty(userId))
            {
                var pets = await _apiService.GetUserPetsAsync(userId);
                Pets.Clear();
                foreach (var pet in pets)
                {
                    Pets.Add(pet);
                }
            }
        });
    }

    [RelayCommand]
    private async Task AddPetAsync()
    {
        await _navigationService.NavigateToAsync("petdetail");
    }

    [RelayCommand]
    private async Task EditPetAsync(PetDto pet)
    {
        var parameters = new Dictionary<string, object>
        {
            { "pet", pet }
        };
        await _navigationService.NavigateToAsync("petdetail", parameters);
    }

    [RelayCommand]
    private async Task DeletePetAsync(PetDto pet)
    {
        var confirm = await _dialogService.ShowConfirmAsync("Delete Pet", $"Are you sure you want to delete {pet.Name}?");
        if (confirm)
        {
            await ExecuteAsync(async () =>
            {
                var success = await _apiService.DeletePetAsync(pet.Id);
                if (success)
                {
                    Pets.Remove(pet);
                    await _dialogService.ShowAlertAsync("Success", "Pet deleted successfully!");
                }
                else
                {
                    await _dialogService.ShowAlertAsync("Error", "Failed to delete pet. Please try again.");
                }
            });
        }
    }

    [RelayCommand]
    private async Task RefreshAsync()
    {
        LoadPets();
    }
}
