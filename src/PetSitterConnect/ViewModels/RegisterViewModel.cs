using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using PetSitterConnect.Core.DTOs;
using PetSitterConnect.Core.Models;
using PetSitterConnect.Services;

namespace PetSitterConnect.ViewModels;

public partial class RegisterViewModel : BaseViewModel
{
    private readonly IAuthService _authService;

    [ObservableProperty]
    private string _firstName = string.Empty;

    [ObservableProperty]
    private string _lastName = string.Empty;

    [ObservableProperty]
    private string _email = string.Empty;

    [ObservableProperty]
    private string _password = string.Empty;

    [ObservableProperty]
    private string _confirmPassword = string.Empty;

    [ObservableProperty]
    private string _phoneNumber = string.Empty;

    [ObservableProperty]
    private UserType _userType = UserType.PetOwner;

    [ObservableProperty]
    private bool _agreeToTerms;

    public RegisterViewModel(IAuthService authService, INavigationService navigationService, IDialogService dialogService)
        : base(navigationService, dialogService)
    {
        _authService = authService;
        Title = "Create Account";
    }

    [RelayCommand]
    private async Task RegisterAsync()
    {
        if (!ValidateForm())
            return;

        await ExecuteAsync(async () =>
        {
            var registerDto = new CreateUserDto
            {
                FirstName = FirstName,
                LastName = LastName,
                Email = Email,
                Password = Password,
                PhoneNumber = PhoneNumber,
                UserType = UserType
            };

            var success = await _authService.RegisterAsync(registerDto);
            
            if (success)
            {
                Application.Current.MainPage = new AppShell();
            }
            else
            {
                await _dialogService.ShowAlertAsync("Registration Failed", "Unable to create account. Please try again.");
            }
        });
    }

    [RelayCommand]
    private async Task NavigateToLoginAsync()
    {
        await _navigationService.NavigateToAsync("//login");
    }

    private bool ValidateForm()
    {
        if (string.IsNullOrWhiteSpace(FirstName) || string.IsNullOrWhiteSpace(LastName) ||
            string.IsNullOrWhiteSpace(Email) || string.IsNullOrWhiteSpace(Password))
        {
            _dialogService.ShowAlertAsync("Validation Error", "Please fill in all required fields.");
            return false;
        }

        if (Password != ConfirmPassword)
        {
            _dialogService.ShowAlertAsync("Validation Error", "Passwords do not match.");
            return false;
        }

        if (!AgreeToTerms)
        {
            _dialogService.ShowAlertAsync("Validation Error", "Please agree to the terms and conditions.");
            return false;
        }

        return true;
    }
}
