using CommunityToolkit.Mvvm.Input;
using PetSitterConnect.Services;
using PetSitterConnect.Views;

namespace PetSitterConnect.ViewModels;

public partial class AppShellViewModel
{
    private readonly IAuthService _authService;
    private readonly IDialogService _dialogService;

    public AppShellViewModel(IAuthService authService, IDialogService dialogService)
    {
        _authService = authService;
        _dialogService = dialogService;
    }

    [RelayCommand]
    private async Task LogoutAsync()
    {
        var confirm = await _dialogService.ShowConfirmAsync("Logout", "Are you sure you want to logout?");
        if (confirm)
        {
            await _authService.LogoutAsync();
            Application.Current.MainPage = new NavigationPage(new LoginPage(
                ServiceHelper.GetService<LoginViewModel>()));
        }
    }
}
