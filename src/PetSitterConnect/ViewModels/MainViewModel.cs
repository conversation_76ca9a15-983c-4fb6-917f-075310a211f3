using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using PetSitterConnect.Services;

namespace PetSitterConnect.ViewModels;

public partial class MainViewModel : BaseViewModel
{
    private readonly IAuthService _authService;

    [ObservableProperty]
    private string _welcomeMessage = string.Empty;

    public MainViewModel(IAuthService authService, INavigationService navigationService, IDialogService dialogService)
        : base(navigationService, dialogService)
    {
        _authService = authService;
        Title = "Home";
        LoadWelcomeMessage();
    }

    private async void LoadWelcomeMessage()
    {
        var user = await _authService.GetCurrentUserAsync();
        if (user != null)
        {
            WelcomeMessage = $"Welcome back, {user.FirstName}!";
        }
    }

    [RelayCommand]
    private async Task NavigateToRequestsAsync()
    {
        await _navigationService.NavigateToAsync("//requests");
    }

    [RelayCommand]
    private async Task NavigateToPetsAsync()
    {
        await _navigationService.NavigateToAsync("//pets");
    }

    [RelayCommand]
    private async Task NavigateToBookingsAsync()
    {
        await _navigationService.NavigateToAsync("//bookings");
    }
}
