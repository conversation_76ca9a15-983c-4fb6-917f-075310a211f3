<?xml version="1.0" encoding="UTF-8" ?>
<Application x:Class="PetSitterConnect.App"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:local="clr-namespace:PetSitterConnect"
             xmlns:converters="clr-namespace:PetSitterConnect.Converters">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Resources/Styles/Colors.xaml" />
                <ResourceDictionary Source="Resources/Styles/Styles.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Converters -->
            <converters:InvertedBoolConverter x:Key="InvertedBoolConverter" />
            <converters:BoolToStringConverter x:Key="BoolToStringConverter" />
            <converters:StringToBoolConverter x:Key="StringToBoolConverter" />
            <converters:EnumToIntConverter x:Key="EnumToIntConverter" />
            <converters:StatusToColorConverter x:Key="StatusToColorConverter" />
            
            <!-- App Colors -->
            <Color x:Key="Primary">#512BD4</Color>
            <Color x:Key="Secondary">#DFD8F7</Color>
            <Color x:Key="Tertiary">#2B0B98</Color>
            <Color x:Key="White">White</Color>
            <Color x:Key="Black">Black</Color>
            <Color x:Key="Gray100">#E1E1E1</Color>
            <Color x:Key="Gray200">#C8C8C8</Color>
            <Color x:Key="Gray300">#ACACAC</Color>
            <Color x:Key="Gray400">#919191</Color>
            <Color x:Key="Gray500">#6E6E6E</Color>
            <Color x:Key="Gray600">#404040</Color>
            <Color x:Key="Gray900">#212121</Color>
            <Color x:Key="Gray950">#141414</Color>
            
            <!-- Pet Care Specific Colors -->
            <Color x:Key="PetPrimary">#FF6B35</Color>
            <Color x:Key="PetSecondary">#F7931E</Color>
            <Color x:Key="PetAccent">#FFD23F</Color>
            <Color x:Key="PetSuccess">#4CAF50</Color>
            <Color x:Key="PetWarning">#FF9800</Color>
            <Color x:Key="PetError">#F44336</Color>
            
        </ResourceDictionary>
    </Application.Resources>
</Application>
