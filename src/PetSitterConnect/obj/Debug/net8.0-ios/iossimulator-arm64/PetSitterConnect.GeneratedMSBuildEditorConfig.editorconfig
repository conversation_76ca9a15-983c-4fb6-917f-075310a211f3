is_global = true
build_property.EnableAotAnalyzer = 
build_property.EnableSingleFileAnalyzer = 
build_property.EnableTrimAnalyzer = false
build_property.IncludeAllContentForSelfExtract = 
build_property.TargetFramework = net8.0-ios
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = false
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = PetSitterConnect
build_property.ProjectDir = /Users/<USER>/Documents/PetCare /src/PetSitterConnect/
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.EffectiveAnalysisLevelStyle = 8.0
build_property.EnableCodeStyleSeverity = 

[/Users/<USER>/Documents/PetCare /src/PetSitterConnect/App.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = PetSitterConnect.App.xaml
build_metadata.AdditionalFiles.TargetPath = App.xaml
build_metadata.AdditionalFiles.RelativePath = App.xaml

[/Users/<USER>/Documents/PetCare /src/PetSitterConnect/AppShell.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = PetSitterConnect.AppShell.xaml
build_metadata.AdditionalFiles.TargetPath = AppShell.xaml
build_metadata.AdditionalFiles.RelativePath = AppShell.xaml

[/Users/<USER>/Documents/PetCare /src/PetSitterConnect/Resources/Styles/Colors.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = PetSitterConnect.Resources.Styles.Colors.xaml
build_metadata.AdditionalFiles.TargetPath = Resources/Styles/Colors.xaml
build_metadata.AdditionalFiles.RelativePath = Resources/Styles/Colors.xaml

[/Users/<USER>/Documents/PetCare /src/PetSitterConnect/Resources/Styles/Styles.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = PetSitterConnect.Resources.Styles.Styles.xaml
build_metadata.AdditionalFiles.TargetPath = Resources/Styles/Styles.xaml
build_metadata.AdditionalFiles.RelativePath = Resources/Styles/Styles.xaml

[/Users/<USER>/Documents/PetCare /src/PetSitterConnect/Views/BookingDetailPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = PetSitterConnect.Views.BookingDetailPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views/BookingDetailPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views/BookingDetailPage.xaml

[/Users/<USER>/Documents/PetCare /src/PetSitterConnect/Views/BookingListPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = PetSitterConnect.Views.BookingListPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views/BookingListPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views/BookingListPage.xaml

[/Users/<USER>/Documents/PetCare /src/PetSitterConnect/Views/ChatPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = PetSitterConnect.Views.ChatPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views/ChatPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views/ChatPage.xaml

[/Users/<USER>/Documents/PetCare /src/PetSitterConnect/Views/CreateRequestPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = PetSitterConnect.Views.CreateRequestPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views/CreateRequestPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views/CreateRequestPage.xaml

[/Users/<USER>/Documents/PetCare /src/PetSitterConnect/Views/HelpPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = PetSitterConnect.Views.HelpPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views/HelpPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views/HelpPage.xaml

[/Users/<USER>/Documents/PetCare /src/PetSitterConnect/Views/LoginPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = PetSitterConnect.Views.LoginPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views/LoginPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views/LoginPage.xaml

[/Users/<USER>/Documents/PetCare /src/PetSitterConnect/Views/MainPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = PetSitterConnect.Views.MainPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views/MainPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views/MainPage.xaml

[/Users/<USER>/Documents/PetCare /src/PetSitterConnect/Views/PetDetailPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = PetSitterConnect.Views.PetDetailPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views/PetDetailPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views/PetDetailPage.xaml

[/Users/<USER>/Documents/PetCare /src/PetSitterConnect/Views/PetListPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = PetSitterConnect.Views.PetListPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views/PetListPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views/PetListPage.xaml

[/Users/<USER>/Documents/PetCare /src/PetSitterConnect/Views/ProfilePage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = PetSitterConnect.Views.ProfilePage.xaml
build_metadata.AdditionalFiles.TargetPath = Views/ProfilePage.xaml
build_metadata.AdditionalFiles.RelativePath = Views/ProfilePage.xaml

[/Users/<USER>/Documents/PetCare /src/PetSitterConnect/Views/RegisterPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = PetSitterConnect.Views.RegisterPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views/RegisterPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views/RegisterPage.xaml

[/Users/<USER>/Documents/PetCare /src/PetSitterConnect/Views/RequestDetailPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = PetSitterConnect.Views.RequestDetailPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views/RequestDetailPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views/RequestDetailPage.xaml

[/Users/<USER>/Documents/PetCare /src/PetSitterConnect/Views/RequestListPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = PetSitterConnect.Views.RequestListPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views/RequestListPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views/RequestListPage.xaml

[/Users/<USER>/Documents/PetCare /src/PetSitterConnect/Views/SettingsPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = PetSitterConnect.Views.SettingsPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views/SettingsPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views/SettingsPage.xaml
