{"format": 1, "restore": {"/Users/<USER>/Documents/PetCare /src/PetSitterConnect/PetSitterConnect.csproj": {}}, "projects": {"/Users/<USER>/Documents/PetCare /src/PetSitterConnect.Core/PetSitterConnect.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/PetCare /src/PetSitterConnect.Core/PetSitterConnect.Core.csproj", "projectName": "PetSitterConnect.Core", "projectPath": "/Users/<USER>/Documents/PetCare /src/PetSitterConnect.Core/PetSitterConnect.Core.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/PetCare /src/PetSitterConnect.Core/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"/usr/local/share/dotnet/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.17, 8.0.17]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.301/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"iossimulator-arm64": {"#import": []}}}, "/Users/<USER>/Documents/PetCare /src/PetSitterConnect/PetSitterConnect.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/PetCare /src/PetSitterConnect/PetSitterConnect.csproj", "projectName": "PetSitterConnect", "projectPath": "/Users/<USER>/Documents/PetCare /src/PetSitterConnect/PetSitterConnect.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/PetCare /src/PetSitterConnect/obj/", "projectStyle": "PackageReference", "crossTargeting": true, "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0-android", "net8.0-ios", "net8.0-maccatalyst"], "sources": {"/usr/local/share/dotnet/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-android34.0": {"targetAlias": "net8.0-android", "projectReferences": {"/Users/<USER>/Documents/PetCare /src/PetSitterConnect.Core/PetSitterConnect.Core.csproj": {"projectPath": "/Users/<USER>/Documents/PetCare /src/PetSitterConnect.Core/PetSitterConnect.Core.csproj"}}}, "net8.0-ios18.0": {"targetAlias": "net8.0-ios", "projectReferences": {"/Users/<USER>/Documents/PetCare /src/PetSitterConnect.Core/PetSitterConnect.Core.csproj": {"projectPath": "/Users/<USER>/Documents/PetCare /src/PetSitterConnect.Core/PetSitterConnect.Core.csproj"}}}, "net8.0-maccatalyst18.0": {"targetAlias": "net8.0-maccatalyst", "projectReferences": {"/Users/<USER>/Documents/PetCare /src/PetSitterConnect.Core/PetSitterConnect.Core.csproj": {"projectPath": "/Users/<USER>/Documents/PetCare /src/PetSitterConnect.Core/PetSitterConnect.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-android34.0": {"targetAlias": "net8.0-android", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.2, )"}, "Microsoft.AspNetCore.SignalR.Client": {"target": "Package", "version": "[8.0.8, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Maui.Controls": {"target": "Package", "version": "[8.0.91, )"}, "Microsoft.Maui.Controls.Compatibility": {"target": "Package", "version": "[8.0.91, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[8.0.17, )", "autoReferenced": true}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.17, 8.0.17]"}], "frameworkReferences": {"Microsoft.Android": {"privateAssets": "all"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.301/PortableRuntimeIdentifierGraph.json"}, "net8.0-ios18.0": {"targetAlias": "net8.0-ios", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.2, )"}, "Microsoft.AspNetCore.SignalR.Client": {"target": "Package", "version": "[8.0.8, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Maui.Controls": {"target": "Package", "version": "[8.0.91, )"}, "Microsoft.Maui.Controls.Compatibility": {"target": "Package", "version": "[8.0.91, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[8.0.17, )", "autoReferenced": true}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.iOS.Ref.net8.0_18.0", "version": "[18.0.8324, 18.0.8324]"}, {"name": "Microsoft.iOS.Runtime.iossimulator-arm64.net8.0_18.0", "version": "[18.0.8324, 18.0.8324]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.17, 8.0.17]"}], "frameworkReferences": {"Microsoft.iOS": {"privateAssets": "all"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.301/PortableRuntimeIdentifierGraph.json"}, "net8.0-maccatalyst18.0": {"targetAlias": "net8.0-maccatalyst", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.2, )"}, "Microsoft.AspNetCore.SignalR.Client": {"target": "Package", "version": "[8.0.8, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Maui.Controls": {"target": "Package", "version": "[8.0.91, )"}, "Microsoft.Maui.Controls.Compatibility": {"target": "Package", "version": "[8.0.91, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[8.0.17, )", "autoReferenced": true}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["xamarinios10", "net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.MacCatalyst.Ref.net8.0_18.0", "version": "[18.0.8324, 18.0.8324]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.17, 8.0.17]"}], "frameworkReferences": {"Microsoft.MacCatalyst": {"privateAssets": "all"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.301/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"iossimulator-arm64": {"#import": []}}}}}