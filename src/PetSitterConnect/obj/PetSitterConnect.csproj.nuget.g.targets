﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(TargetFramework)' == 'net8.0-android' AND '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)xamarin.kotlin.stdlib.common/1.9.0.1/buildTransitive/net6.0-android31.0/Xamarin.Kotlin.StdLib.Common.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.kotlin.stdlib.common/1.9.0.1/buildTransitive/net6.0-android31.0/Xamarin.Kotlin.StdLib.Common.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.jetbrains.annotations/24.0.1.3/buildTransitive/net6.0-android31.0/Xamarin.Jetbrains.Annotations.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.jetbrains.annotations/24.0.1.3/buildTransitive/net6.0-android31.0/Xamarin.Jetbrains.Annotations.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.kotlin.stdlib/1.9.0.1/buildTransitive/net6.0-android31.0/Xamarin.Kotlin.StdLib.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.kotlin.stdlib/1.9.0.1/buildTransitive/net6.0-android31.0/Xamarin.Kotlin.StdLib.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.kotlin.stdlib.jdk7/1.9.0.1/buildTransitive/net6.0-android31.0/Xamarin.Kotlin.StdLib.Jdk7.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.kotlin.stdlib.jdk7/1.9.0.1/buildTransitive/net6.0-android31.0/Xamarin.Kotlin.StdLib.Jdk7.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.kotlin.stdlib.jdk8/1.9.0.1/buildTransitive/net6.0-android31.0/Xamarin.Kotlin.StdLib.Jdk8.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.kotlin.stdlib.jdk8/1.9.0.1/buildTransitive/net6.0-android31.0/Xamarin.Kotlin.StdLib.Jdk8.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.kotlinx.coroutines.core.jvm/1.7.2.1/buildTransitive/net6.0-android31.0/Xamarin.KotlinX.Coroutines.Core.Jvm.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.kotlinx.coroutines.core.jvm/1.7.2.1/buildTransitive/net6.0-android31.0/Xamarin.KotlinX.Coroutines.Core.Jvm.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.kotlinx.coroutines.android/1.7.2.1/buildTransitive/net6.0-android31.0/Xamarin.KotlinX.Coroutines.Android.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.kotlinx.coroutines.android/1.7.2.1/buildTransitive/net6.0-android31.0/Xamarin.KotlinX.Coroutines.Android.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.google.guava.listenablefuture/*******4/buildTransitive/net6.0-android31.0/Xamarin.Google.Guava.ListenableFuture.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.google.guava.listenablefuture/*******4/buildTransitive/net6.0-android31.0/Xamarin.Google.Guava.ListenableFuture.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.annotation.jvm/1.6.0.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Annotation.Jvm.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.annotation.jvm/1.6.0.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Annotation.Jvm.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.annotation/1.6.0.4/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Annotation.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.annotation/1.6.0.4/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Annotation.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.collection/1.2.0.9/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Collection.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.collection/1.2.0.9/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Collection.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.versionedparcelable/1.1.1.19/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.VersionedParcelable.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.versionedparcelable/1.1.1.19/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.VersionedParcelable.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.tracing.tracing/1.1.0.6/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Tracing.Tracing.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.tracing.tracing/1.1.0.6/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Tracing.Tracing.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.startup.startupruntime/1.1.1.7/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Startup.StartupRuntime.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.startup.startupruntime/1.1.1.7/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Startup.StartupRuntime.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.concurrent.futures/*******4/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Concurrent.Futures.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.concurrent.futures/*******4/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Concurrent.Futures.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.profileinstaller.profileinstaller/1.3.1.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.ProfileInstaller.ProfileInstaller.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.profileinstaller.profileinstaller/1.3.1.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.ProfileInstaller.ProfileInstaller.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.lifecycle.common/2.6.1.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.Common.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.lifecycle.common/2.6.1.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.Common.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.arch.core.common/2.2.0.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Arch.Core.Common.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.arch.core.common/2.2.0.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Arch.Core.Common.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.arch.core.runtime/2.2.0.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Arch.Core.Runtime.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.arch.core.runtime/2.2.0.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Arch.Core.Runtime.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.lifecycle.runtime/2.6.1.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.Runtime.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.lifecycle.runtime/2.6.1.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.Runtime.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.interpolator/*******9/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Interpolator.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.interpolator/*******9/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Interpolator.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.annotation.experimental/1.3.1.1/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Annotation.Experimental.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.annotation.experimental/1.3.1.1/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Annotation.Experimental.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.core/1.10.1.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Core.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.core/1.10.1.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Core.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.customview/*******8/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.CustomView.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.customview/*******8/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.CustomView.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.viewpager/*******9/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.ViewPager.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.viewpager/*******9/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.ViewPager.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.savedstate/*******/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.SavedState.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.savedstate/*******/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.SavedState.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.core.core.ktx/1.10.1.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Core.Core.Ktx.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.core.core.ktx/1.10.1.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Core.Core.Ktx.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.customview.poolingcontainer/1.0.0.5/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.CustomView.PoolingContainer.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.customview.poolingcontainer/1.0.0.5/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.CustomView.PoolingContainer.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.recyclerview/1.3.0.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.RecyclerView.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.recyclerview/1.3.0.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.RecyclerView.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.lifecycle.viewmodel/2.6.1.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.ViewModel.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.lifecycle.viewmodel/2.6.1.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.ViewModel.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.lifecycle.livedata.core/2.6.1.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.LiveData.Core.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.lifecycle.livedata.core/2.6.1.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.LiveData.Core.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.loader/*******9/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Loader.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.loader/*******9/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Loader.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.lifecycle.viewmodelsavedstate/2.6.1.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.ViewModelSavedState.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.lifecycle.viewmodelsavedstate/2.6.1.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.ViewModelSavedState.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.activity/1.7.2.1/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Activity.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.activity/1.7.2.1/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Activity.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.fragment/1.6.0.1/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Fragment.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.fragment/1.6.0.1/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Fragment.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.viewpager2/1.0.0.21/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.ViewPager2.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.viewpager2/1.0.0.21/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.ViewPager2.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.vectordrawable/*******9/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.VectorDrawable.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.vectordrawable/*******9/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.VectorDrawable.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.vectordrawable.animated/*******9/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.VectorDrawable.Animated.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.vectordrawable.animated/*******9/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.VectorDrawable.Animated.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.transition/1.4.1.12/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Transition.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.transition/1.4.1.12/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Transition.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.resourceinspection.annotation/1.0.1.7/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.ResourceInspection.Annotation.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.resourceinspection.annotation/1.0.1.7/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.ResourceInspection.Annotation.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.print/*******9/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Print.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.print/*******9/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Print.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.localbroadcastmanager/1.1.0.7/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.LocalBroadcastManager.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.localbroadcastmanager/1.1.0.7/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.LocalBroadcastManager.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.lifecycle.process/2.6.1.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.Process.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.lifecycle.process/2.6.1.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.Process.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.documentfile/1.0.1.19/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.DocumentFile.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.documentfile/1.0.1.19/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.DocumentFile.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.legacy.support.core.utils/*******9/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Legacy.Support.Core.Utils.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.legacy.support.core.utils/*******9/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Legacy.Support.Core.Utils.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.emoji2/1.3.0.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Emoji2.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.emoji2/1.3.0.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Emoji2.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.emoji2.viewshelper/1.3.0.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Emoji2.ViewsHelper.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.emoji2.viewshelper/1.3.0.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Emoji2.ViewsHelper.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.dynamicanimation/*******9/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.DynamicAnimation.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.dynamicanimation/*******9/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.DynamicAnimation.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.drawerlayout/1.2.0.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.DrawerLayout.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.drawerlayout/1.2.0.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.DrawerLayout.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.cursoradapter/*******9/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.CursorAdapter.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.cursoradapter/*******9/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.CursorAdapter.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.coordinatorlayout/*******/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.CoordinatorLayout.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.coordinatorlayout/*******/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.CoordinatorLayout.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.constraintlayout.core/1.0.4.6/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.ConstraintLayout.Core.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.constraintlayout.core/1.0.4.6/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.ConstraintLayout.Core.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.appcompat.appcompatresources/1.6.1.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.AppCompat.AppCompatResources.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.appcompat.appcompatresources/1.6.1.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.AppCompat.AppCompatResources.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.appcompat/1.6.1.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.AppCompat.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.appcompat/1.6.1.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.AppCompat.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.constraintlayout/2.1.4.6/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.ConstraintLayout.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.constraintlayout/2.1.4.6/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.ConstraintLayout.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.cardview/1.0.0.21/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.CardView.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.cardview/1.0.0.21/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.CardView.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.google.android.material/1.9.0.2/buildTransitive/net6.0-android31.0/Xamarin.Google.Android.Material.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.google.android.material/1.9.0.2/buildTransitive/net6.0-android31.0/Xamarin.Google.Android.Material.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.window.extensions.core.core/*******/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Window.Extensions.Core.Core.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.window.extensions.core.core/*******/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Window.Extensions.Core.Core.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.window/*******/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Window.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.window/*******/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Window.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.swiperefreshlayout/*******4/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.SwipeRefreshLayout.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.swiperefreshlayout/*******4/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.SwipeRefreshLayout.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.slidingpanelayout/*******/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.SlidingPaneLayout.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.slidingpanelayout/*******/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.SlidingPaneLayout.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.security.securitycrypto/*******-alpha06/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Security.SecurityCrypto.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.security.securitycrypto/*******-alpha06/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Security.SecurityCrypto.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.savedstate.savedstate.ktx/*******/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.SavedState.SavedState.Ktx.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.savedstate.savedstate.ktx/*******/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.SavedState.SavedState.Ktx.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.lifecycle.viewmodel.ktx/2.6.1.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.ViewModel.Ktx.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.lifecycle.viewmodel.ktx/2.6.1.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.ViewModel.Ktx.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.lifecycle.runtime.ktx/2.6.1.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.Runtime.Ktx.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.lifecycle.runtime.ktx/2.6.1.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.Runtime.Ktx.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.collection.ktx/1.2.0.9/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Collection.Ktx.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.collection.ktx/1.2.0.9/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Collection.Ktx.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.navigation.common/2.6.0.1/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Navigation.Common.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.navigation.common/2.6.0.1/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Navigation.Common.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.activity.ktx/1.7.2.1/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Activity.Ktx.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.activity.ktx/1.7.2.1/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Activity.Ktx.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.navigation.runtime/2.6.0.1/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Navigation.Runtime.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.navigation.runtime/2.6.0.1/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Navigation.Runtime.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.navigation.ui/2.6.0.1/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Navigation.UI.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.navigation.ui/2.6.0.1/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Navigation.UI.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.lifecycle.livedata.core.ktx/2.6.1.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.LiveData.Core.Ktx.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.lifecycle.livedata.core.ktx/2.6.1.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.LiveData.Core.Ktx.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.fragment.ktx/1.6.0.1/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Fragment.Ktx.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.fragment.ktx/1.6.0.1/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Fragment.Ktx.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.navigation.fragment/2.6.0.1/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Navigation.Fragment.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.navigation.fragment/2.6.0.1/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Navigation.Fragment.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.lifecycle.livedata/2.6.1.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.LiveData.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.lifecycle.livedata/2.6.1.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Lifecycle.LiveData.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.exifinterface/1.3.6.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.ExifInterface.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.exifinterface/1.3.6.2/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.ExifInterface.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.androidx.browser/1.5.0.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Browser.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.androidx.browser/1.5.0.3/buildTransitive/net6.0-android31.0/Xamarin.AndroidX.Browser.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.android.glide.gifdecoder/4.15.1.2/buildTransitive/net6.0-android31.0/Xamarin.Android.Glide.GifDecoder.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.android.glide.gifdecoder/4.15.1.2/buildTransitive/net6.0-android31.0/Xamarin.Android.Glide.GifDecoder.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.android.glide.disklrucache/4.15.1.2/buildTransitive/net6.0-android31.0/Xamarin.Android.Glide.DiskLruCache.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.android.glide.disklrucache/4.15.1.2/buildTransitive/net6.0-android31.0/Xamarin.Android.Glide.DiskLruCache.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.android.glide.annotations/4.15.1.2/buildTransitive/net6.0-android31.0/Xamarin.Android.Glide.Annotations.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.android.glide.annotations/4.15.1.2/buildTransitive/net6.0-android31.0/Xamarin.Android.Glide.Annotations.targets')" />
    <Import Project="$(NuGetPackageRoot)xamarin.android.glide/4.15.1.2/buildTransitive/net6.0-android31.0/Xamarin.Android.Glide.targets" Condition="Exists('$(NuGetPackageRoot)xamarin.android.glide/4.15.1.2/buildTransitive/net6.0-android31.0/Xamarin.Android.Glide.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.maui.resizetizer/8.0.91/buildTransitive/Microsoft.Maui.Resizetizer.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.maui.resizetizer/8.0.91/buildTransitive/Microsoft.Maui.Resizetizer.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.options/8.0.2/buildTransitive/net6.0/Microsoft.Extensions.Options.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.options/8.0.2/buildTransitive/net6.0/Microsoft.Extensions.Options.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.logging.abstractions/8.0.1/buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.logging.abstractions/8.0.1/buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.maui.core/8.0.91/buildTransitive/Microsoft.Maui.Core.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.maui.core/8.0.91/buildTransitive/Microsoft.Maui.Core.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.maui.controls.build.tasks/8.0.91/buildTransitive/netstandard2.0/Microsoft.Maui.Controls.Build.Tasks.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.maui.controls.build.tasks/8.0.91/buildTransitive/netstandard2.0/Microsoft.Maui.Controls.Build.Tasks.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.binder/8.0.0/buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.binder/8.0.0/buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets')" />
    <Import Project="$(NuGetPackageRoot)communitytoolkit.mvvm/8.2.2/buildTransitive/netstandard2.1/CommunityToolkit.Mvvm.targets" Condition="Exists('$(NuGetPackageRoot)communitytoolkit.mvvm/8.2.2/buildTransitive/netstandard2.1/CommunityToolkit.Mvvm.targets')" />
  </ImportGroup>
  <ImportGroup Condition=" '$(TargetFramework)' == 'net8.0-ios' AND '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.maui.resizetizer/8.0.91/buildTransitive/Microsoft.Maui.Resizetizer.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.maui.resizetizer/8.0.91/buildTransitive/Microsoft.Maui.Resizetizer.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.options/8.0.2/buildTransitive/net6.0/Microsoft.Extensions.Options.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.options/8.0.2/buildTransitive/net6.0/Microsoft.Extensions.Options.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.logging.abstractions/8.0.1/buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.logging.abstractions/8.0.1/buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.maui.core/8.0.91/buildTransitive/Microsoft.Maui.Core.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.maui.core/8.0.91/buildTransitive/Microsoft.Maui.Core.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.maui.controls.build.tasks/8.0.91/buildTransitive/net6.0-ios10.0/Microsoft.Maui.Controls.Build.Tasks.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.maui.controls.build.tasks/8.0.91/buildTransitive/net6.0-ios10.0/Microsoft.Maui.Controls.Build.Tasks.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.binder/8.0.0/buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.binder/8.0.0/buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets')" />
    <Import Project="$(NuGetPackageRoot)communitytoolkit.mvvm/8.2.2/buildTransitive/netstandard2.1/CommunityToolkit.Mvvm.targets" Condition="Exists('$(NuGetPackageRoot)communitytoolkit.mvvm/8.2.2/buildTransitive/netstandard2.1/CommunityToolkit.Mvvm.targets')" />
  </ImportGroup>
  <ImportGroup Condition=" '$(TargetFramework)' == 'net8.0-maccatalyst' AND '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.maui.resizetizer/8.0.91/buildTransitive/Microsoft.Maui.Resizetizer.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.maui.resizetizer/8.0.91/buildTransitive/Microsoft.Maui.Resizetizer.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.options/8.0.2/buildTransitive/net6.0/Microsoft.Extensions.Options.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.options/8.0.2/buildTransitive/net6.0/Microsoft.Extensions.Options.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.logging.abstractions/8.0.1/buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.logging.abstractions/8.0.1/buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.maui.core/8.0.91/buildTransitive/Microsoft.Maui.Core.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.maui.core/8.0.91/buildTransitive/Microsoft.Maui.Core.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.maui.controls.build.tasks/8.0.91/buildTransitive/net6.0-maccatalyst13.1/Microsoft.Maui.Controls.Build.Tasks.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.maui.controls.build.tasks/8.0.91/buildTransitive/net6.0-maccatalyst13.1/Microsoft.Maui.Controls.Build.Tasks.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.binder/8.0.0/buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.binder/8.0.0/buildTransitive/netstandard2.0/Microsoft.Extensions.Configuration.Binder.targets')" />
    <Import Project="$(NuGetPackageRoot)communitytoolkit.mvvm/8.2.2/buildTransitive/netstandard2.1/CommunityToolkit.Mvvm.targets" Condition="Exists('$(NuGetPackageRoot)communitytoolkit.mvvm/8.2.2/buildTransitive/netstandard2.1/CommunityToolkit.Mvvm.targets')" />
  </ImportGroup>
</Project>