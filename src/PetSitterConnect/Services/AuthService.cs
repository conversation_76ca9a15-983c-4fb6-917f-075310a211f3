using PetSitterConnect.Core.DTOs;

namespace PetSitterConnect.Services;

public class AuthService : IAuthService
{
    private readonly IApiService _apiService;
    private readonly ILocalStorageService _localStorage;
    private UserDto? _currentUser;

    public event EventHandler<bool>? AuthenticationStateChanged;

    public AuthService(IApiService apiService, ILocalStorageService localStorage)
    {
        _apiService = apiService;
        _localStorage = localStorage;
    }

    public async Task<bool> LoginAsync(string email, string password)
    {
        try
        {
            var loginDto = new LoginDto { Email = email, Password = password };
            var response = await _apiService.LoginAsync(loginDto);

            if (response != null)
            {
                // Store token and user info
                _localStorage.SetValue("auth_token", response.Token);
                _localStorage.SetValue("user_id", response.User.Id);
                _localStorage.SetValue("token_expires", response.ExpiresAt);
                
                _currentUser = response.User;
                _apiService.SetAuthToken(response.Token);
                
                AuthenticationStateChanged?.Invoke(this, true);
                return true;
            }
            
            return false;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Login Error: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> RegisterAsync(CreateUserDto registerDto)
    {
        try
        {
            var response = await _apiService.RegisterAsync(registerDto);

            if (response != null)
            {
                // Store token and user info
                _localStorage.SetValue("auth_token", response.Token);
                _localStorage.SetValue("user_id", response.User.Id);
                _localStorage.SetValue("token_expires", response.ExpiresAt);
                
                _currentUser = response.User;
                _apiService.SetAuthToken(response.Token);
                
                AuthenticationStateChanged?.Invoke(this, true);
                return true;
            }
            
            return false;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Register Error: {ex.Message}");
            return false;
        }
    }

    public async Task LogoutAsync()
    {
        // Clear stored data
        _localStorage.RemoveValue("auth_token");
        _localStorage.RemoveValue("user_id");
        _localStorage.RemoveValue("token_expires");
        
        _currentUser = null;
        _apiService.ClearAuthToken();
        
        AuthenticationStateChanged?.Invoke(this, false);
        
        await Task.CompletedTask;
    }

    public async Task<bool> IsAuthenticatedAsync()
    {
        var token = _localStorage.GetValue<string>("auth_token");
        var expiresAt = _localStorage.GetValue<DateTime?>("token_expires");

        if (string.IsNullOrEmpty(token) || expiresAt == null || expiresAt <= DateTime.UtcNow)
        {
            await LogoutAsync();
            return false;
        }

        if (_currentUser == null)
        {
            _currentUser = await _apiService.GetCurrentUserAsync();
        }

        return _currentUser != null;
    }

    public async Task<UserDto?> GetCurrentUserAsync()
    {
        if (_currentUser == null && await IsAuthenticatedAsync())
        {
            _currentUser = await _apiService.GetCurrentUserAsync();
        }
        
        return _currentUser;
    }

    public string? GetCurrentUserId()
    {
        return _localStorage.GetValue<string>("user_id");
    }
}
