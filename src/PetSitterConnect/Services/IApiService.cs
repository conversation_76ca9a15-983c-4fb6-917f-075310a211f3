using PetSitterConnect.Core.DTOs;

namespace PetSitterConnect.Services;

public interface IApiService
{
    Task<T?> GetAsync<T>(string endpoint);
    Task<T?> PostAsync<T>(string endpoint, object data);
    Task<T?> PutAsync<T>(string endpoint, object data);
    Task<bool> DeleteAsync(string endpoint);
    Task<AuthResponseDto?> LoginAsync(LoginDto loginDto);
    Task<AuthResponseDto?> RegisterAsync(CreateUserDto registerDto);
    Task<UserDto?> GetCurrentUserAsync();
    Task<List<PetDto>> GetUserPetsAsync(string userId);
    Task<PetDto?> CreatePetAsync(CreatePetDto petDto);
    Task<PetDto?> UpdatePetAsync(string petId, UpdatePetDto petDto);
    Task<bool> DeletePetAsync(string petId);
    void SetAuthToken(string token);
    void ClearAuthToken();
}
