namespace PetSitterConnect.Services;

public class DialogService : IDialogService
{
    public async Task ShowAlertAsync(string title, string message, string cancel = "OK")
    {
        await Application.Current.MainPage.DisplayAlert(title, message, cancel);
    }

    public async Task<bool> ShowConfirmAsync(string title, string message, string accept = "Yes", string cancel = "No")
    {
        return await Application.Current.MainPage.DisplayAlert(title, message, accept, cancel);
    }

    public async Task<string> ShowPromptAsync(string title, string message, string accept = "OK", string cancel = "Cancel", string placeholder = "", int maxLength = -1, Keyboard keyboard = null, string initialValue = "")
    {
        return await Application.Current.MainPage.DisplayPromptAsync(title, message, accept, cancel, placeholder, maxLength, keyboard, initialValue);
    }

    public async Task ShowLoadingAsync(string message = "Loading...")
    {
        // Implementation would depend on your loading UI component
        // For now, this is a placeholder
        await Task.CompletedTask;
    }

    public async Task HideLoadingAsync()
    {
        // Implementation would depend on your loading UI component
        // For now, this is a placeholder
        await Task.CompletedTask;
    }
}
