using Newtonsoft.Json;

namespace PetSitterConnect.Services;

public class LocalStorageService : ILocalStorageService
{
    public void SetValue<T>(string key, T value)
    {
        try
        {
            var json = JsonConvert.SerializeObject(value);
            Preferences.Set(key, json);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"LocalStorage SetValue Error: {ex.Message}");
        }
    }

    public T? GetValue<T>(string key)
    {
        try
        {
            var json = Preferences.Get(key, string.Empty);
            if (string.IsNullOrEmpty(json))
                return default;

            return JsonConvert.DeserializeObject<T>(json);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"LocalStorage GetValue Error: {ex.Message}");
            return default;
        }
    }

    public void RemoveValue(string key)
    {
        try
        {
            Preferences.Remove(key);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"LocalStorage RemoveValue Error: {ex.Message}");
        }
    }

    public bool ContainsKey(string key)
    {
        try
        {
            return Preferences.ContainsKey(key);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"LocalStorage ContainsKey Error: {ex.Message}");
            return false;
        }
    }

    public void Clear()
    {
        try
        {
            Preferences.Clear();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"LocalStorage Clear Error: {ex.Message}");
        }
    }
}
