using PetSitterConnect.Core.DTOs;

namespace PetSitterConnect.Services;

public interface IAuthService
{
    Task<bool> LoginAsync(string email, string password);
    Task<bool> RegisterAsync(CreateUserDto registerDto);
    Task LogoutAsync();
    Task<bool> IsAuthenticatedAsync();
    Task<UserDto?> GetCurrentUserAsync();
    string? GetCurrentUserId();
    event EventHandler<bool> AuthenticationStateChanged;
}
