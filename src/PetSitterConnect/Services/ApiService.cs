using System.Net.Http.Headers;
using System.Text;
using Newtonsoft.Json;
using PetSitterConnect.Core.DTOs;

namespace PetSitterConnect.Services;

public class ApiService : IApiService
{
    private readonly HttpClient _httpClient;
    private readonly ILocalStorageService _localStorage;

    public ApiService(IHttpClientFactory httpClientFactory, ILocalStorageService localStorage)
    {
        _httpClient = httpClientFactory.CreateClient("PetSitterApi");
        _localStorage = localStorage;
        
        // Set auth token if available
        var token = _localStorage.GetValue<string>("auth_token");
        if (!string.IsNullOrEmpty(token))
        {
            SetAuthToken(token);
        }
    }

    public async Task<T?> GetAsync<T>(string endpoint)
    {
        try
        {
            var response = await _httpClient.GetAsync(endpoint);
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<T>(json);
            }
            return default;
        }
        catch (Exception ex)
        {
            // Log error
            System.Diagnostics.Debug.WriteLine($"API GET Error: {ex.Message}");
            return default;
        }
    }

    public async Task<T?> PostAsync<T>(string endpoint, object data)
    {
        try
        {
            var json = JsonConvert.SerializeObject(data);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync(endpoint, content);
            if (response.IsSuccessStatusCode)
            {
                var responseJson = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<T>(responseJson);
            }
            return default;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"API POST Error: {ex.Message}");
            return default;
        }
    }

    public async Task<T?> PutAsync<T>(string endpoint, object data)
    {
        try
        {
            var json = JsonConvert.SerializeObject(data);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PutAsync(endpoint, content);
            if (response.IsSuccessStatusCode)
            {
                var responseJson = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<T>(responseJson);
            }
            return default;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"API PUT Error: {ex.Message}");
            return default;
        }
    }

    public async Task<bool> DeleteAsync(string endpoint)
    {
        try
        {
            var response = await _httpClient.DeleteAsync(endpoint);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"API DELETE Error: {ex.Message}");
            return false;
        }
    }

    public async Task<AuthResponseDto?> LoginAsync(LoginDto loginDto)
    {
        return await PostAsync<AuthResponseDto>("auth/login", loginDto);
    }

    public async Task<AuthResponseDto?> RegisterAsync(CreateUserDto registerDto)
    {
        return await PostAsync<AuthResponseDto>("auth/register", registerDto);
    }

    public async Task<UserDto?> GetCurrentUserAsync()
    {
        return await GetAsync<UserDto>("users/me");
    }

    public async Task<List<PetDto>> GetUserPetsAsync(string userId)
    {
        var pets = await GetAsync<List<PetDto>>($"users/{userId}/pets");
        return pets ?? new List<PetDto>();
    }

    public async Task<PetDto?> CreatePetAsync(CreatePetDto petDto)
    {
        return await PostAsync<PetDto>("pets", petDto);
    }

    public async Task<PetDto?> UpdatePetAsync(string petId, UpdatePetDto petDto)
    {
        return await PutAsync<PetDto>($"pets/{petId}", petDto);
    }

    public async Task<bool> DeletePetAsync(string petId)
    {
        return await DeleteAsync($"pets/{petId}");
    }

    // Pet Care Requests
    public async Task<List<PetCareRequestDto>> GetPetCareRequestsAsync(int page = 1, int pageSize = 10)
    {
        var requests = await GetAsync<List<PetCareRequestDto>>($"petcarerequests?page={page}&pageSize={pageSize}");
        return requests ?? new List<PetCareRequestDto>();
    }

    public async Task<List<PetCareRequestDto>> GetMyPetCareRequestsAsync()
    {
        var requests = await GetAsync<List<PetCareRequestDto>>("petcarerequests/my-requests");
        return requests ?? new List<PetCareRequestDto>();
    }

    public async Task<PetCareRequestDto?> CreatePetCareRequestAsync(CreatePetCareRequestDto requestDto)
    {
        return await PostAsync<PetCareRequestDto>("petcarerequests", requestDto);
    }

    // Sitter Profiles
    public async Task<List<SitterProfileDto>> GetAvailableSittersAsync(int page = 1, int pageSize = 10)
    {
        var sitters = await GetAsync<List<SitterProfileDto>>($"sitterprofiles/available?page={page}&pageSize={pageSize}");
        return sitters ?? new List<SitterProfileDto>();
    }

    public async Task<SitterProfileDto?> GetMySitterProfileAsync()
    {
        return await GetAsync<SitterProfileDto>("sitterprofiles/me");
    }

    public async Task<SitterProfileDto?> CreateSitterProfileAsync(CreateSitterProfileDto profileDto)
    {
        return await PostAsync<SitterProfileDto>("sitterprofiles", profileDto);
    }

    public async Task<SitterProfileDto?> UpdateSitterProfileAsync(UpdateSitterProfileDto profileDto)
    {
        return await PutAsync<SitterProfileDto>("sitterprofiles/me", profileDto);
    }

    public void SetAuthToken(string token)
    {
        _httpClient.DefaultRequestHeaders.Authorization =
            new AuthenticationHeaderValue("Bearer", token);
    }

    public void ClearAuthToken()
    {
        _httpClient.DefaultRequestHeaders.Authorization = null;
    }
}
