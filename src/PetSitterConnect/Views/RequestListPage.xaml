<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="PetSitterConnect.Views.RequestListPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:PetSitterConnect.ViewModels"
             Title="{Binding Title}">



    <ContentPage.ToolbarItems>
        <ToolbarItem Text="Create Request" Command="{Binding CreateRequestCommand}" />
    </ContentPage.ToolbarItems>

    <Grid RowDefinitions="Auto,*">
        
        <!-- Search Bar -->
        <Frame Grid.Row="0" 
               BackgroundColor="{StaticResource Gray100}" 
               CornerRadius="10" 
               Margin="15,10">
            <Grid ColumnDefinitions="*,Auto">
                <Entry Grid.Column="0" 
                       Text="{Binding SearchText}" 
                       Placeholder="Search pet care requests..."
                       BackgroundColor="Transparent" />
                <Button Grid.Column="1" 
                        Text="Search" 
                        Command="{Binding SearchCommand}"
                        BackgroundColor="{StaticResource PetPrimary}"
                        TextColor="White"
                        CornerRadius="5" />
            </Grid>
        </Frame>

        <!-- Requests List -->
        <RefreshView Grid.Row="1" 
                     IsRefreshing="{Binding IsRefreshing}" 
                     Command="{Binding RefreshCommand}">
            <CollectionView ItemsSource="{Binding Requests}">
                <CollectionView.EmptyView>
                    <StackLayout Padding="20" VerticalOptions="Center">
                        <Image Source="empty_requests.png" 
                               HeightRequest="100" 
                               WidthRequest="100" 
                               HorizontalOptions="Center" />
                        <Label Text="No pet care requests found" 
                               FontSize="18" 
                               HorizontalOptions="Center" 
                               TextColor="{StaticResource Gray600}" />
                        <Label Text="Create your first request to find a pet sitter" 
                               FontSize="14" 
                               HorizontalOptions="Center" 
                               TextColor="{StaticResource Gray500}" />
                    </StackLayout>
                </CollectionView.EmptyView>
                
                <CollectionView.ItemTemplate>
                    <DataTemplate>
                        <Frame Margin="15,5" 
                               BackgroundColor="White" 
                               CornerRadius="10" 
                               HasShadow="True">
                            <Grid RowDefinitions="Auto,Auto,Auto,Auto" 
                                  ColumnDefinitions="*,Auto" 
                                  Padding="10">
                                
                                <!-- Title -->
                                <Label Grid.Row="0" Grid.ColumnSpan="2" 
                                       Text="{Binding Title}" 
                                       FontSize="18" 
                                       FontAttributes="Bold" 
                                       TextColor="{StaticResource Gray900}" />
                                
                                <!-- Pet Info -->
                                <Label Grid.Row="1" Grid.Column="0" 
                                       Text="{Binding Pet.Name, StringFormat='Pet: {0}'}" 
                                       FontSize="14" 
                                       TextColor="{StaticResource Gray600}" />
                                
                                <!-- Date Range -->
                                <Label Grid.Row="2" Grid.Column="0" 
                                       Text="{Binding StartDate, StringFormat='From: {0:MMM dd, yyyy}'}" 
                                       FontSize="14" 
                                       TextColor="{StaticResource Gray600}" />
                                
                                <!-- Budget -->
                                <Label Grid.Row="3" Grid.Column="0" 
                                       Text="{Binding Budget, StringFormat='Budget: ${0:F2}'}" 
                                       FontSize="16" 
                                       FontAttributes="Bold" 
                                       TextColor="{StaticResource PetPrimary}" />
                                
                                <!-- Status Badge -->
                                <Frame Grid.Row="0" Grid.Column="1" 
                                       BackgroundColor="{Binding Status, Converter={StaticResource StatusToColorConverter}}" 
                                       CornerRadius="12" 
                                       Padding="8,4" 
                                       VerticalOptions="Start">
                                    <Label Text="{Binding Status}" 
                                           FontSize="12" 
                                           TextColor="White" 
                                           FontAttributes="Bold" />
                                </Frame>
                                
                                <!-- Tap Gesture -->
                                <Grid.GestureRecognizers>
                                    <TapGestureRecognizer Command="{Binding Source={RelativeSource AncestorType={x:Type vm:RequestListViewModel}}, Path=ViewRequestCommand}"
                                                          CommandParameter="{Binding .}" />
                                </Grid.GestureRecognizers>
                                
                            </Grid>
                        </Frame>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
        </RefreshView>

    </Grid>

</ContentPage>
