<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="PetSitterConnect.Views.HelpPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             Title="Help & Support">

    <ScrollView>
        <StackLayout Padding="20" Spacing="20">
            
            <Label Text="Help & Support" 
                   FontSize="28" 
                   FontAttributes="Bold" 
                   TextColor="{StaticResource PetPrimary}" 
                   HorizontalOptions="Center" />

            <!-- FAQ Section -->
            <Frame BackgroundColor="White" CornerRadius="10" Padding="15">
                <StackLayout Spacing="15">
                    <Label Text="Frequently Asked Questions" FontSize="18" FontAttributes="Bold" />
                    
                    <StackLayout>
                        <Label Text="How do I create a pet care request?" 
                               FontSize="16" 
                               FontAttributes="Bold" 
                               TextColor="{StaticResource Gray900}" />
                        <Label Text="Go to the Requests tab and tap the '+' button to create a new request. Fill in the details about your pet and the care you need." 
                               FontSize="14" 
                               TextColor="{StaticResource Gray600}" />
                    </StackLayout>
                    
                    <StackLayout>
                        <Label Text="How do I become a pet sitter?" 
                               FontSize="16" 
                               FontAttributes="Bold" 
                               TextColor="{StaticResource Gray900}" />
                        <Label Text="Go to your Profile and create a Sitter Profile with your experience, rates, and availability." 
                               FontSize="14" 
                               TextColor="{StaticResource Gray600}" />
                    </StackLayout>
                    
                    <StackLayout>
                        <Label Text="How are payments handled?" 
                               FontSize="16" 
                               FontAttributes="Bold" 
                               TextColor="{StaticResource Gray900}" />
                        <Label Text="Payments are processed securely through our platform. Funds are held in escrow until the service is completed." 
                               FontSize="14" 
                               TextColor="{StaticResource Gray600}" />
                    </StackLayout>
                </StackLayout>
            </Frame>

            <!-- Contact Section -->
            <Frame BackgroundColor="White" CornerRadius="10" Padding="15">
                <StackLayout Spacing="15">
                    <Label Text="Contact Us" FontSize="18" FontAttributes="Bold" />
                    
                    <Button Text="Send Feedback" 
                            BackgroundColor="{StaticResource PetPrimary}" 
                            TextColor="White" />
                    
                    <Button Text="Report an Issue" 
                            BackgroundColor="{StaticResource PetWarning}" 
                            TextColor="White" />
                    
                    <StackLayout>
                        <Label Text="Email: <EMAIL>" 
                               FontSize="14" 
                               TextColor="{StaticResource Gray600}" />
                        <Label Text="Phone: 1-800-PET-CARE" 
                               FontSize="14" 
                               TextColor="{StaticResource Gray600}" />
                    </StackLayout>
                </StackLayout>
            </Frame>

            <!-- App Info -->
            <Frame BackgroundColor="White" CornerRadius="10" Padding="15">
                <StackLayout Spacing="10">
                    <Label Text="App Information" FontSize="18" FontAttributes="Bold" />
                    <Label Text="Version: 1.0.0" FontSize="14" TextColor="{StaticResource Gray600}" />
                    <Label Text="© 2024 PetSitter Connect" FontSize="14" TextColor="{StaticResource Gray600}" />
                </StackLayout>
            </Frame>

        </StackLayout>
    </ScrollView>

</ContentPage>
