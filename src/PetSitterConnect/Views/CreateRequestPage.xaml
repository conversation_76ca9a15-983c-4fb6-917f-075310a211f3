<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="PetSitterConnect.Views.CreateRequestPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:PetSitterConnect.ViewModels"
             Title="{Binding Title}">

    <ContentPage.BindingContext>
        <vm:CreateRequestViewModel />
    </ContentPage.BindingContext>

    <ScrollView>
        <StackLayout Padding="20" Spacing="20">
            
            <!-- Header -->
            <Label Text="Create Pet Care Request" 
                   FontSize="24" 
                   FontAttributes="Bold" 
                   TextColor="{StaticResource PetPrimary}" 
                   HorizontalOptions="Center" />

            <!-- Request Form -->
            <StackLayout Spacing="15">
                
                <!-- Title -->
                <StackLayout>
                    <Label Text="Request Title" FontSize="16" TextColor="{StaticResource Gray900}" />
                    <Entry Text="{Binding Title}" 
                           Placeholder="e.g., Weekend pet sitting for my dog" />
                </StackLayout>

                <!-- Pet Selection -->
                <StackLayout>
                    <Label Text="Select Pet" FontSize="16" TextColor="{StaticResource Gray900}" />
                    <Picker Title="Choose your pet" 
                            ItemsSource="{Binding UserPets}"
                            ItemDisplayBinding="{Binding Name}"
                            SelectedItem="{Binding SelectedPet}" />
                </StackLayout>

                <!-- Date Range -->
                <Grid ColumnDefinitions="*,10,*">
                    <StackLayout Grid.Column="0">
                        <Label Text="Start Date" FontSize="16" TextColor="{StaticResource Gray900}" />
                        <DatePicker Date="{Binding StartDate}" 
                                    MinimumDate="{Binding MinDate}" />
                    </StackLayout>
                    <StackLayout Grid.Column="2">
                        <Label Text="End Date" FontSize="16" TextColor="{StaticResource Gray900}" />
                        <DatePicker Date="{Binding EndDate}" 
                                    MinimumDate="{Binding StartDate}" />
                    </StackLayout>
                </Grid>

                <!-- Budget -->
                <StackLayout>
                    <Label Text="Budget ($)" FontSize="16" TextColor="{StaticResource Gray900}" />
                    <Entry Text="{Binding Budget}" 
                           Placeholder="Enter your budget"
                           Keyboard="Numeric" />
                </StackLayout>

                <!-- Description -->
                <StackLayout>
                    <Label Text="Description" FontSize="16" TextColor="{StaticResource Gray900}" />
                    <Editor Text="{Binding Description}" 
                            Placeholder="Describe what you need..."
                            HeightRequest="100" />
                </StackLayout>

                <!-- Special Instructions -->
                <StackLayout>
                    <Label Text="Special Instructions" FontSize="16" TextColor="{StaticResource Gray900}" />
                    <Editor Text="{Binding SpecialInstructions}" 
                            Placeholder="Any special care instructions..."
                            HeightRequest="80" />
                </StackLayout>

                <!-- Service Type -->
                <StackLayout>
                    <Label Text="Service Type" FontSize="16" TextColor="{StaticResource Gray900}" />
                    <StackLayout Orientation="Horizontal" Spacing="20">
                        <StackLayout Orientation="Horizontal">
                            <RadioButton IsChecked="{Binding IsHostingService}" />
                            <Label Text="Pet Hosting (at sitter's place)" VerticalOptions="Center" />
                        </StackLayout>
                        <StackLayout Orientation="Horizontal">
                            <RadioButton IsChecked="{Binding IsHostingService, Converter={StaticResource InvertedBoolConverter}}" />
                            <Label Text="Pet Sitting (at my place)" VerticalOptions="Center" />
                        </StackLayout>
                    </StackLayout>
                </StackLayout>

                <!-- Create Button -->
                <Button Text="Create Request" 
                        Command="{Binding SaveRequestCommand}"
                        BackgroundColor="{StaticResource PetPrimary}"
                        TextColor="White"
                        FontSize="18"
                        HeightRequest="50"
                        CornerRadius="25"
                        Margin="0,20,0,0"
                        IsEnabled="{Binding IsBusy, Converter={StaticResource InvertedBoolConverter}}" />

                <!-- Loading Indicator -->
                <ActivityIndicator IsVisible="{Binding IsBusy}" 
                                   IsRunning="{Binding IsBusy}" 
                                   Color="{StaticResource PetPrimary}" />

            </StackLayout>

        </StackLayout>
    </ScrollView>

</ContentPage>
