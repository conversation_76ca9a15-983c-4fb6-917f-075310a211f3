<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="PetSitterConnect.Views.ProfilePage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:PetSitterConnect.ViewModels"
             Title="{Binding Title}">

    <ContentPage.BindingContext>
        <vm:ProfileViewModel />
    </ContentPage.BindingContext>

    <ScrollView>
        <StackLayout Padding="20" Spacing="20">
            
            <!-- Profile Header -->
            <Frame BackgroundColor="{StaticResource PetPrimary}" 
                   CornerRadius="15" 
                   Padding="20">
                <StackLayout>
                    <Ellipse Fill="{StaticResource Gray300}" 
                             HeightRequest="80" 
                             WidthRequest="80" 
                             HorizontalOptions="Center" />
                    <Label Text="{Binding FirstName}" 
                           FontSize="24" 
                           FontAttributes="Bold" 
                           TextColor="White" 
                           HorizontalOptions="Center" />
                    <Label Text="{Binding Email}" 
                           FontSize="16" 
                           TextColor="White" 
                           Opacity="0.9" 
                           HorizontalOptions="Center" />
                </StackLayout>
            </Frame>

            <!-- Profile Form -->
            <StackLayout Spacing="15">
                
                <Label Text="Personal Information" 
                       FontSize="18" 
                       FontAttributes="Bold" 
                       TextColor="{StaticResource Gray900}" />

                <Grid ColumnDefinitions="*,10,*">
                    <StackLayout Grid.Column="0">
                        <Label Text="First Name" FontSize="14" TextColor="{StaticResource Gray900}" />
                        <Entry Text="{Binding FirstName}" />
                    </StackLayout>
                    <StackLayout Grid.Column="2">
                        <Label Text="Last Name" FontSize="14" TextColor="{StaticResource Gray900}" />
                        <Entry Text="{Binding LastName}" />
                    </StackLayout>
                </Grid>

                <StackLayout>
                    <Label Text="Phone Number" FontSize="14" TextColor="{StaticResource Gray900}" />
                    <Entry Text="{Binding PhoneNumber}" Keyboard="Telephone" />
                </StackLayout>

                <StackLayout>
                    <Label Text="Bio" FontSize="14" TextColor="{StaticResource Gray900}" />
                    <Editor Text="{Binding Bio}" 
                            Placeholder="Tell us about yourself..."
                            HeightRequest="100" />
                </StackLayout>

                <!-- Action Buttons -->
                <Button Text="Save Changes" 
                        Command="{Binding SaveProfileCommand}"
                        BackgroundColor="{StaticResource PetPrimary}"
                        TextColor="White"
                        FontSize="16"
                        HeightRequest="45"
                        CornerRadius="22"
                        Margin="0,20,0,0" />

                <Button Text="Logout" 
                        Command="{Binding LogoutCommand}"
                        BackgroundColor="{StaticResource PetError}"
                        TextColor="White"
                        FontSize="16"
                        HeightRequest="45"
                        CornerRadius="22" />

            </StackLayout>

        </StackLayout>
    </ScrollView>

</ContentPage>
