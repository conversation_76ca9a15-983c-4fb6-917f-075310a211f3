<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="PetSitterConnect.Views.LoginPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:PetSitterConnect.ViewModels"
             Title="{Binding Title}"
             Shell.NavBarIsVisible="False">



    <ScrollView>
        <Grid RowDefinitions="Auto,*,Auto" Padding="20">
            
            <!-- Header -->
            <StackLayout Grid.Row="0" Spacing="10" Margin="0,50,0,30">
                <Image Source="logo.png" 
                       HeightRequest="100" 
                       WidthRequest="100" 
                       HorizontalOptions="Center" />
                <Label Text="PetSitter Connect" 
                       FontSize="28" 
                       FontAttributes="Bold" 
                       HorizontalOptions="Center" 
                       TextColor="{StaticResource PetPrimary}" />
                <Label Text="Welcome back! Please sign in to your account." 
                       FontSize="16" 
                       HorizontalOptions="Center" 
                       TextColor="{StaticResource Gray600}" />
            </StackLayout>

            <!-- Login Form -->
            <StackLayout Grid.Row="1" Spacing="20">
                
                <!-- Email -->
                <StackLayout>
                    <Label Text="Email" FontSize="16" TextColor="{StaticResource Gray900}" />
                    <Entry Text="{Binding Email}" 
                           Placeholder="Enter your email"
                           Keyboard="Email"
                           ReturnType="Next" />
                    <Label Text="{Binding EmailError}" 
                           TextColor="{StaticResource PetError}" 
                           FontSize="12"
                           IsVisible="{Binding EmailError, Converter={StaticResource StringToBoolConverter}}" />
                </StackLayout>

                <!-- Password -->
                <StackLayout>
                    <Label Text="Password" FontSize="16" TextColor="{StaticResource Gray900}" />
                    <Entry Text="{Binding Password}" 
                           Placeholder="Enter your password"
                           IsPassword="True"
                           ReturnType="Done" />
                    <Label Text="{Binding PasswordError}" 
                           TextColor="{StaticResource PetError}" 
                           FontSize="12"
                           IsVisible="{Binding PasswordError, Converter={StaticResource StringToBoolConverter}}" />
                </StackLayout>

                <!-- Remember Me & Forgot Password -->
                <Grid ColumnDefinitions="*,Auto">
                    <CheckBox Grid.Column="0" 
                              IsChecked="{Binding RememberMe}" 
                              VerticalOptions="Center" />
                    <Label Grid.Column="0" 
                           Text="Remember me" 
                           VerticalOptions="Center" 
                           Margin="35,0,0,0" />
                    <Button Grid.Column="1" 
                            Text="Forgot Password?" 
                            BackgroundColor="Transparent" 
                            TextColor="{StaticResource PetPrimary}" 
                            Command="{Binding ForgotPasswordCommand}" />
                </Grid>

                <!-- Login Button -->
                <Button Text="Sign In" 
                        Command="{Binding LoginCommand}"
                        BackgroundColor="{StaticResource PetPrimary}"
                        TextColor="White"
                        FontSize="18"
                        HeightRequest="50"
                        CornerRadius="25"
                        IsEnabled="{Binding IsBusy, Converter={StaticResource InvertedBoolConverter}}" />

                <!-- Loading Indicator -->
                <ActivityIndicator IsVisible="{Binding IsBusy}" 
                                   IsRunning="{Binding IsBusy}" 
                                   Color="{StaticResource PetPrimary}" />

            </StackLayout>

            <!-- Register Link -->
            <StackLayout Grid.Row="2" Orientation="Horizontal" HorizontalOptions="Center" Margin="0,30,0,0">
                <Label Text="Don't have an account?" TextColor="{StaticResource Gray600}" />
                <Button Text="Sign Up" 
                        BackgroundColor="Transparent" 
                        TextColor="{StaticResource PetPrimary}" 
                        Command="{Binding NavigateToRegisterCommand}" />
            </StackLayout>

        </Grid>
    </ScrollView>

</ContentPage>
