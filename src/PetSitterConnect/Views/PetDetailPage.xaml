<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="PetSitterConnect.Views.PetDetailPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:PetSitterConnect.ViewModels"
             Title="{Binding Title}">



    <ScrollView>
        <StackLayout Padding="20" Spacing="20">
            
            <!-- Pet Image -->
            <Frame BackgroundColor="{StaticResource Gray100}" 
                   CornerRadius="15" 
                   HeightRequest="200" 
                   HorizontalOptions="FillAndExpand">
                <Image Source="pet_placeholder.png" 
                       Aspect="AspectFill" />
            </Frame>

            <!-- Pet Information Form -->
            <StackLayout Spacing="15">
                
                <Label Text="Pet Information" 
                       FontSize="20" 
                       FontAttributes="Bold" 
                       TextColor="{StaticResource Gray900}" />

                <!-- Name -->
                <StackLayout>
                    <Label Text="Pet Name" FontSize="14" TextColor="{StaticResource Gray900}" />
                    <Entry Text="{Binding Name}" 
                           Placeholder="Enter pet's name" />
                </StackLayout>

                <!-- Pet Type -->
                <StackLayout>
                    <Label Text="Pet Type" FontSize="14" TextColor="{StaticResource Gray900}" />
                    <Picker Title="Select pet type" 
                            SelectedIndex="{Binding PetType, Converter={StaticResource EnumToIntConverter}}">
                        <Picker.ItemsSource>
                            <x:Array Type="{x:Type x:String}">
                                <x:String>Dog</x:String>
                                <x:String>Cat</x:String>
                                <x:String>Bird</x:String>
                                <x:String>Fish</x:String>
                                <x:String>Rabbit</x:String>
                                <x:String>Hamster</x:String>
                                <x:String>Other</x:String>
                            </x:Array>
                        </Picker.ItemsSource>
                    </Picker>
                </StackLayout>

                <!-- Breed and Age -->
                <Grid ColumnDefinitions="*,10,*">
                    <StackLayout Grid.Column="0">
                        <Label Text="Breed" FontSize="14" TextColor="{StaticResource Gray900}" />
                        <Entry Text="{Binding Breed}" 
                               Placeholder="Breed (optional)" />
                    </StackLayout>
                    <StackLayout Grid.Column="2">
                        <Label Text="Age (years)" FontSize="14" TextColor="{StaticResource Gray900}" />
                        <Entry Text="{Binding Age}" 
                               Placeholder="Age"
                               Keyboard="Numeric" />
                    </StackLayout>
                </Grid>

                <!-- Size -->
                <StackLayout>
                    <Label Text="Size" FontSize="14" TextColor="{StaticResource Gray900}" />
                    <Entry Text="{Binding Size}" 
                           Placeholder="e.g., Small, Medium, Large" />
                </StackLayout>

                <!-- Description -->
                <StackLayout>
                    <Label Text="Description" FontSize="14" TextColor="{StaticResource Gray900}" />
                    <Editor Text="{Binding Description}" 
                            Placeholder="Tell us about your pet..."
                            HeightRequest="100" />
                </StackLayout>

                <!-- Special Needs -->
                <StackLayout>
                    <Label Text="Special Needs" FontSize="14" TextColor="{StaticResource Gray900}" />
                    <Editor Text="{Binding SpecialNeeds}" 
                            Placeholder="Any special care requirements..."
                            HeightRequest="80" />
                </StackLayout>

                <!-- Medical Conditions -->
                <StackLayout>
                    <Label Text="Medical Conditions" FontSize="14" TextColor="{StaticResource Gray900}" />
                    <Editor Text="{Binding MedicalConditions}" 
                            Placeholder="Any medical conditions..."
                            HeightRequest="80" />
                </StackLayout>

                <!-- Checkboxes -->
                <StackLayout Spacing="10">
                    <StackLayout Orientation="Horizontal">
                        <CheckBox IsChecked="{Binding IsVaccinated}" />
                        <Label Text="Vaccinated" VerticalOptions="Center" />
                    </StackLayout>
                    <StackLayout Orientation="Horizontal">
                        <CheckBox IsChecked="{Binding IsNeutered}" />
                        <Label Text="Spayed/Neutered" VerticalOptions="Center" />
                    </StackLayout>
                </StackLayout>

                <!-- Save Button -->
                <Button Text="{Binding IsEditMode, Converter={StaticResource BoolToStringConverter}, ConverterParameter='Update Pet|Add Pet'}" 
                        Command="{Binding SavePetCommand}"
                        BackgroundColor="{StaticResource PetPrimary}"
                        TextColor="White"
                        FontSize="18"
                        HeightRequest="50"
                        CornerRadius="25"
                        Margin="0,20,0,0"
                        IsEnabled="{Binding IsBusy, Converter={StaticResource InvertedBoolConverter}}" />

                <!-- Loading Indicator -->
                <ActivityIndicator IsVisible="{Binding IsBusy}" 
                                   IsRunning="{Binding IsBusy}" 
                                   Color="{StaticResource PetPrimary}" />

            </StackLayout>

        </StackLayout>
    </ScrollView>

</ContentPage>
