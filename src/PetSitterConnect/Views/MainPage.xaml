<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="PetSitterConnect.Views.MainPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:PetSitterConnect.ViewModels"
             Title="{Binding Title}">



    <ScrollView>
        <StackLayout Padding="20" Spacing="20">
            
            <!-- Welcome Section -->
            <Frame BackgroundColor="{StaticResource PetPrimary}" 
                   CornerRadius="15" 
                   Padding="20">
                <StackLayout>
                    <Label Text="{Binding WelcomeMessage}" 
                           FontSize="24" 
                           FontAttributes="Bold" 
                           TextColor="White" />
                    <Label Text="What would you like to do today?" 
                           FontSize="16" 
                           TextColor="White" 
                           Opacity="0.9" />
                </StackLayout>
            </Frame>

            <!-- Quick Actions -->
            <Label Text="Quick Actions" 
                   FontSize="20" 
                   FontAttributes="Bold" 
                   TextColor="{StaticResource Gray900}" />

            <Grid RowDefinitions="Auto,Auto" ColumnDefinitions="*,*" RowSpacing="15" ColumnSpacing="15">
                
                <!-- Find Pet Sitters -->
                <Frame Grid.Row="0" Grid.Column="0" 
                       BackgroundColor="{StaticResource PetSecondary}" 
                       CornerRadius="10" 
                       Padding="15">
                    <StackLayout>
                        <Image Source="search_icon.png" 
                               HeightRequest="40" 
                               WidthRequest="40" 
                               HorizontalOptions="Center" />
                        <Label Text="Find Sitters" 
                               FontSize="16" 
                               FontAttributes="Bold" 
                               HorizontalOptions="Center" 
                               TextColor="White" />
                    </StackLayout>
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding NavigateToRequestsCommand}" />
                    </Frame.GestureRecognizers>
                </Frame>

                <!-- My Pets -->
                <Frame Grid.Row="0" Grid.Column="1" 
                       BackgroundColor="{StaticResource PetAccent}" 
                       CornerRadius="10" 
                       Padding="15">
                    <StackLayout>
                        <Image Source="pets_icon.png" 
                               HeightRequest="40" 
                               WidthRequest="40" 
                               HorizontalOptions="Center" />
                        <Label Text="My Pets" 
                               FontSize="16" 
                               FontAttributes="Bold" 
                               HorizontalOptions="Center" 
                               TextColor="White" />
                    </StackLayout>
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding NavigateToPetsCommand}" />
                    </Frame.GestureRecognizers>
                </Frame>

                <!-- My Bookings -->
                <Frame Grid.Row="1" Grid.Column="0" 
                       BackgroundColor="{StaticResource PetSuccess}" 
                       CornerRadius="10" 
                       Padding="15">
                    <StackLayout>
                        <Image Source="calendar_icon.png" 
                               HeightRequest="40" 
                               WidthRequest="40" 
                               HorizontalOptions="Center" />
                        <Label Text="Bookings" 
                               FontSize="16" 
                               FontAttributes="Bold" 
                               HorizontalOptions="Center" 
                               TextColor="White" />
                    </StackLayout>
                    <Frame.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding NavigateToBookingsCommand}" />
                    </Frame.GestureRecognizers>
                </Frame>

                <!-- Messages -->
                <Frame Grid.Row="1" Grid.Column="1" 
                       BackgroundColor="{StaticResource Primary}" 
                       CornerRadius="10" 
                       Padding="15">
                    <StackLayout>
                        <Image Source="chat_icon.png" 
                               HeightRequest="40" 
                               WidthRequest="40" 
                               HorizontalOptions="Center" />
                        <Label Text="Messages" 
                               FontSize="16" 
                               FontAttributes="Bold" 
                               HorizontalOptions="Center" 
                               TextColor="White" />
                    </StackLayout>
                </Frame>

            </Grid>

            <!-- Recent Activity -->
            <Label Text="Recent Activity" 
                   FontSize="20" 
                   FontAttributes="Bold" 
                   TextColor="{StaticResource Gray900}" 
                   Margin="0,20,0,0" />

            <Frame BackgroundColor="{StaticResource Gray100}" 
                   CornerRadius="10" 
                   Padding="20">
                <Label Text="No recent activity to show." 
                       FontSize="16" 
                       TextColor="{StaticResource Gray600}" 
                       HorizontalOptions="Center" />
            </Frame>

        </StackLayout>
    </ScrollView>

</ContentPage>
