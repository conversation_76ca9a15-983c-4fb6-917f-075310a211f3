<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="PetSitterConnect.Views.ChatPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:PetSitterConnect.ViewModels"
             Title="{Binding Title}">

    <ContentPage.BindingContext>
        <vm:ChatViewModel />
    </ContentPage.BindingContext>

    <Grid RowDefinitions="*,Auto">
        
        <!-- Messages List -->
        <CollectionView Grid.Row="0" 
                        ItemsSource="{Binding Messages}"
                        VerticalScrollBarVisibility="Always">
            <CollectionView.EmptyView>
                <StackLayout Padding="20" VerticalOptions="Center">
                    <Label Text="No messages yet" 
                           FontSize="18" 
                           HorizontalOptions="Center" 
                           TextColor="{StaticResource Gray600}" />
                    <Label Text="Start the conversation!" 
                           FontSize="14" 
                           HorizontalOptions="Center" 
                           TextColor="{StaticResource Gray500}" />
                </StackLayout>
            </CollectionView.EmptyView>
            
            <CollectionView.ItemTemplate>
                <DataTemplate>
                    <Grid Padding="15,5" 
                          ColumnDefinitions="*,*">
                        
                        <!-- Received Message -->
                        <Frame Grid.Column="0" 
                               BackgroundColor="{StaticResource Gray100}" 
                               CornerRadius="15" 
                               Padding="10"
                               HorizontalOptions="Start"
                               IsVisible="{Binding IsFromCurrentUser, Converter={StaticResource InvertedBoolConverter}}">
                            <StackLayout>
                                <Label Text="{Binding Sender.FirstName}" 
                                       FontSize="12" 
                                       FontAttributes="Bold" 
                                       TextColor="{StaticResource Gray600}" />
                                <Label Text="{Binding Message}" 
                                       FontSize="16" 
                                       TextColor="{StaticResource Gray900}" />
                                <Label Text="{Binding SentAt, StringFormat='{0:HH:mm}'}" 
                                       FontSize="10" 
                                       TextColor="{StaticResource Gray500}" 
                                       HorizontalOptions="End" />
                            </StackLayout>
                        </Frame>
                        
                        <!-- Sent Message -->
                        <Frame Grid.Column="1" 
                               BackgroundColor="{StaticResource PetPrimary}" 
                               CornerRadius="15" 
                               Padding="10"
                               HorizontalOptions="End"
                               IsVisible="{Binding IsFromCurrentUser}">
                            <StackLayout>
                                <Label Text="{Binding Message}" 
                                       FontSize="16" 
                                       TextColor="White" />
                                <Label Text="{Binding SentAt, StringFormat='{0:HH:mm}'}" 
                                       FontSize="10" 
                                       TextColor="White" 
                                       Opacity="0.8"
                                       HorizontalOptions="End" />
                            </StackLayout>
                        </Frame>
                        
                    </Grid>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>

        <!-- Message Input -->
        <Frame Grid.Row="1" 
               BackgroundColor="{StaticResource Gray100}" 
               CornerRadius="0" 
               Padding="10">
            <Grid ColumnDefinitions="*,Auto">
                <Entry Grid.Column="0" 
                       Text="{Binding MessageText}" 
                       Placeholder="Type a message..."
                       BackgroundColor="White"
                       ReturnType="Send"
                       ReturnCommand="{Binding SendMessageCommand}" />
                <Button Grid.Column="1" 
                        Text="Send" 
                        Command="{Binding SendMessageCommand}"
                        BackgroundColor="{StaticResource PetPrimary}"
                        TextColor="White"
                        CornerRadius="20"
                        WidthRequest="80" />
            </Grid>
        </Frame>

    </Grid>

</ContentPage>
