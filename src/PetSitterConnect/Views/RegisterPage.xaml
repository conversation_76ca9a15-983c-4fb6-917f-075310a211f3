<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="PetSitterConnect.Views.RegisterPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:PetSitterConnect.ViewModels"
             Title="{Binding Title}"
             Shell.NavBarIsVisible="False">



    <ScrollView>
        <Grid RowDefinitions="Auto,*,Auto" Padding="20">
            
            <!-- Header -->
            <StackLayout Grid.Row="0" Spacing="10" Margin="0,30,0,20">
                <Label Text="Create Account" 
                       FontSize="28" 
                       FontAttributes="Bold" 
                       HorizontalOptions="Center" 
                       TextColor="{StaticResource PetPrimary}" />
                <Label Text="Join PetSitter Connect today!" 
                       FontSize="16" 
                       HorizontalOptions="Center" 
                       TextColor="{StaticResource Gray600}" />
            </StackLayout>

            <!-- Registration Form -->
            <StackLayout Grid.Row="1" Spacing="15">
                
                <!-- Name Fields -->
                <Grid ColumnDefinitions="*,10,*">
                    <StackLayout Grid.Column="0">
                        <Label Text="First Name" FontSize="14" TextColor="{StaticResource Gray900}" />
                        <Entry Text="{Binding FirstName}" 
                               Placeholder="First name" />
                    </StackLayout>
                    <StackLayout Grid.Column="2">
                        <Label Text="Last Name" FontSize="14" TextColor="{StaticResource Gray900}" />
                        <Entry Text="{Binding LastName}" 
                               Placeholder="Last name" />
                    </StackLayout>
                </Grid>

                <!-- Email -->
                <StackLayout>
                    <Label Text="Email" FontSize="14" TextColor="{StaticResource Gray900}" />
                    <Entry Text="{Binding Email}" 
                           Placeholder="Enter your email"
                           Keyboard="Email" />
                </StackLayout>

                <!-- Phone -->
                <StackLayout>
                    <Label Text="Phone Number" FontSize="14" TextColor="{StaticResource Gray900}" />
                    <Entry Text="{Binding PhoneNumber}" 
                           Placeholder="Enter your phone number"
                           Keyboard="Telephone" />
                </StackLayout>

                <!-- User Type -->
                <StackLayout>
                    <Label Text="I want to:" FontSize="14" TextColor="{StaticResource Gray900}" />
                    <Picker Title="Select user type" 
                            SelectedIndex="{Binding UserType, Converter={StaticResource EnumToIntConverter}}">
                        <Picker.ItemsSource>
                            <x:Array Type="{x:Type x:String}">
                                <x:String>Find pet sitters for my pets</x:String>
                                <x:String>Provide pet sitting services</x:String>
                                <x:String>Both - I have pets and want to pet sit</x:String>
                            </x:Array>
                        </Picker.ItemsSource>
                    </Picker>
                </StackLayout>

                <!-- Password -->
                <StackLayout>
                    <Label Text="Password" FontSize="14" TextColor="{StaticResource Gray900}" />
                    <Entry Text="{Binding Password}" 
                           Placeholder="Create a password"
                           IsPassword="True" />
                </StackLayout>

                <!-- Confirm Password -->
                <StackLayout>
                    <Label Text="Confirm Password" FontSize="14" TextColor="{StaticResource Gray900}" />
                    <Entry Text="{Binding ConfirmPassword}" 
                           Placeholder="Confirm your password"
                           IsPassword="True" />
                </StackLayout>

                <!-- Terms Agreement -->
                <StackLayout Orientation="Horizontal">
                    <CheckBox IsChecked="{Binding AgreeToTerms}" 
                              VerticalOptions="Center" />
                    <Label Text="I agree to the Terms of Service and Privacy Policy" 
                           VerticalOptions="Center" 
                           FontSize="12"
                           TextColor="{StaticResource Gray600}" />
                </StackLayout>

                <!-- Register Button -->
                <Button Text="Create Account" 
                        Command="{Binding RegisterCommand}"
                        BackgroundColor="{StaticResource PetPrimary}"
                        TextColor="White"
                        FontSize="18"
                        HeightRequest="50"
                        CornerRadius="25"
                        Margin="0,10,0,0"
                        IsEnabled="{Binding IsBusy, Converter={StaticResource InvertedBoolConverter}}" />

                <!-- Loading Indicator -->
                <ActivityIndicator IsVisible="{Binding IsBusy}" 
                                   IsRunning="{Binding IsBusy}" 
                                   Color="{StaticResource PetPrimary}" />

            </StackLayout>

            <!-- Login Link -->
            <StackLayout Grid.Row="2" Orientation="Horizontal" HorizontalOptions="Center" Margin="0,20,0,0">
                <Label Text="Already have an account?" TextColor="{StaticResource Gray600}" />
                <Button Text="Sign In" 
                        BackgroundColor="Transparent" 
                        TextColor="{StaticResource PetPrimary}" 
                        Command="{Binding NavigateToLoginCommand}" />
            </StackLayout>

        </Grid>
    </ScrollView>

</ContentPage>
