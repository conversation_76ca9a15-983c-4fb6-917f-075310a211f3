<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="PetSitterConnect.Views.SettingsPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             Title="Settings">

    <ScrollView>
        <StackLayout Padding="20" Spacing="20">
            
            <Label Text="Settings" 
                   FontSize="28" 
                   FontAttributes="Bold" 
                   TextColor="{StaticResource PetPrimary}" 
                   HorizontalOptions="Center" />

            <!-- Account Settings -->
            <Frame BackgroundColor="White" CornerRadius="10" Padding="15">
                <StackLayout Spacing="15">
                    <Label Text="Account" FontSize="18" FontAttributes="Bold" />
                    
                    <StackLayout Orientation="Horizontal">
                        <Label Text="Notifications" VerticalOptions="Center" />
                        <Switch HorizontalOptions="EndAndExpand" />
                    </StackLayout>
                    
                    <StackLayout Orientation="Horizontal">
                        <Label Text="Location Services" VerticalOptions="Center" />
                        <Switch HorizontalOptions="EndAndExpand" />
                    </StackLayout>
                </StackLayout>
            </Frame>

            <!-- Privacy Settings -->
            <Frame BackgroundColor="White" CornerRadius="10" Padding="15">
                <StackLayout Spacing="15">
                    <Label Text="Privacy" FontSize="18" FontAttributes="Bold" />
                    
                    <StackLayout Orientation="Horizontal">
                        <Label Text="Show Profile to Sitters" VerticalOptions="Center" />
                        <Switch HorizontalOptions="EndAndExpand" />
                    </StackLayout>
                    
                    <StackLayout Orientation="Horizontal">
                        <Label Text="Allow Direct Messages" VerticalOptions="Center" />
                        <Switch HorizontalOptions="EndAndExpand" />
                    </StackLayout>
                </StackLayout>
            </Frame>

            <!-- App Settings -->
            <Frame BackgroundColor="White" CornerRadius="10" Padding="15">
                <StackLayout Spacing="15">
                    <Label Text="App" FontSize="18" FontAttributes="Bold" />
                    
                    <Button Text="Clear Cache" 
                            BackgroundColor="{StaticResource Gray200}" 
                            TextColor="{StaticResource Gray900}" />
                    
                    <Button Text="Terms of Service" 
                            BackgroundColor="Transparent" 
                            TextColor="{StaticResource PetPrimary}" />
                    
                    <Button Text="Privacy Policy" 
                            BackgroundColor="Transparent" 
                            TextColor="{StaticResource PetPrimary}" />
                </StackLayout>
            </Frame>

        </StackLayout>
    </ScrollView>

</ContentPage>
