<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="PetSitterConnect.Views.BookingListPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:PetSitterConnect.ViewModels"
             Title="{Binding Title}">

    <ContentPage.BindingContext>
        <vm:BookingListViewModel />
    </ContentPage.BindingContext>

    <RefreshView IsRefreshing="{Binding IsRefreshing}" Command="{Binding RefreshCommand}">
        <CollectionView ItemsSource="{Binding Bookings}">
            <CollectionView.EmptyView>
                <StackLayout Padding="20" VerticalOptions="Center">
                    <Image Source="empty_bookings.png" 
                           HeightRequest="100" 
                           WidthRequest="100" 
                           HorizontalOptions="Center" />
                    <Label Text="No bookings found" 
                           FontSize="18" 
                           HorizontalOptions="Center" 
                           TextColor="{StaticResource Gray600}" />
                    <Label Text="Your bookings will appear here" 
                           FontSize="14" 
                           HorizontalOptions="Center" 
                           TextColor="{StaticResource Gray500}" />
                </StackLayout>
            </CollectionView.EmptyView>
            
            <CollectionView.ItemTemplate>
                <DataTemplate>
                    <Frame Margin="15,5" 
                           BackgroundColor="White" 
                           CornerRadius="10" 
                           HasShadow="True">
                        <Grid RowDefinitions="Auto,Auto,Auto,Auto" 
                              ColumnDefinitions="*,Auto" 
                              Padding="10">
                            
                            <!-- Pet Care Request Title -->
                            <Label Grid.Row="0" Grid.ColumnSpan="2" 
                                   Text="{Binding PetCareRequest.Title}" 
                                   FontSize="18" 
                                   FontAttributes="Bold" 
                                   TextColor="{StaticResource Gray900}" />
                            
                            <!-- Date Range -->
                            <Label Grid.Row="1" Grid.Column="0" 
                                   Text="{Binding StartDate, StringFormat='From: {0:MMM dd, yyyy}'}" 
                                   FontSize="14" 
                                   TextColor="{StaticResource Gray600}" />
                            
                            <!-- Total Amount -->
                            <Label Grid.Row="2" Grid.Column="0" 
                                   Text="{Binding TotalAmount, StringFormat='Amount: ${0:F2}'}" 
                                   FontSize="16" 
                                   FontAttributes="Bold" 
                                   TextColor="{StaticResource PetPrimary}" />
                            
                            <!-- Status Badge -->
                            <Frame Grid.Row="0" Grid.Column="1" 
                                   BackgroundColor="{Binding Status, Converter={StaticResource StatusToColorConverter}}" 
                                   CornerRadius="12" 
                                   Padding="8,4" 
                                   VerticalOptions="Start">
                                <Label Text="{Binding Status}" 
                                       FontSize="12" 
                                       TextColor="White" 
                                       FontAttributes="Bold" />
                            </Frame>
                            
                            <!-- Tap Gesture -->
                            <Grid.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={RelativeSource AncestorType={x:Type vm:BookingListViewModel}}, Path=ViewBookingCommand}"
                                                      CommandParameter="{Binding .}" />
                            </Grid.GestureRecognizers>
                            
                        </Grid>
                    </Frame>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>
    </RefreshView>

</ContentPage>
