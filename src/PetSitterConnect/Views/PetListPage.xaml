<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="PetSitterConnect.Views.PetListPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:PetSitterConnect.ViewModels"
             Title="{Binding Title}">

    <ContentPage.BindingContext>
        <vm:PetListViewModel />
    </ContentPage.BindingContext>

    <ContentPage.ToolbarItems>
        <ToolbarItem Text="Add Pet" Command="{Binding AddPetCommand}" />
    </ContentPage.ToolbarItems>

    <RefreshView IsRefreshing="{Binding IsRefreshing}" Command="{Binding RefreshCommand}">
        <CollectionView ItemsSource="{Binding Pets}">
            <CollectionView.EmptyView>
                <StackLayout Padding="20" VerticalOptions="Center">
                    <Label Text="No pets found" 
                           FontSize="18" 
                           HorizontalOptions="Center" 
                           TextColor="{StaticResource Gray600}" />
                    <Label Text="Tap the + button to add your first pet" 
                           FontSize="14" 
                           HorizontalOptions="Center" 
                           TextColor="{StaticResource Gray500}" />
                </StackLayout>
            </CollectionView.EmptyView>
            
            <CollectionView.ItemTemplate>
                <DataTemplate>
                    <Grid Padding="15" RowDefinitions="Auto,Auto" ColumnDefinitions="60,*,Auto">
                        <Ellipse Grid.RowSpan="2" 
                                 Fill="{StaticResource PetPrimary}" 
                                 HeightRequest="50" 
                                 WidthRequest="50" />
                        <Label Grid.Column="1" 
                               Text="{Binding Name}" 
                               FontSize="18" 
                               FontAttributes="Bold" />
                        <Label Grid.Row="1" Grid.Column="1" 
                               Text="{Binding Type}" 
                               FontSize="14" 
                               TextColor="{StaticResource Gray600}" />
                        <Button Grid.RowSpan="2" Grid.Column="2" 
                                Text="Edit" 
                                Command="{Binding Source={RelativeSource AncestorType={x:Type vm:PetListViewModel}}, Path=EditPetCommand}"
                                CommandParameter="{Binding .}" />
                    </Grid>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>
    </RefreshView>

</ContentPage>
