using PetSitterConnect.Views;

namespace PetSitterConnect;

public partial class AppShell : Shell
{
    public AppShell()
    {
        InitializeComponent();
        
        // Register routes for navigation
        Routing.RegisterRoute("login", typeof(LoginPage));
        Routing.RegisterRoute("register", typeof(RegisterPage));
        Routing.RegisterRoute("petdetail", typeof(PetDetailPage));
        Routing.RegisterRoute("createrequest", typeof(CreateRequestPage));
        Routing.RegisterRoute("chat", typeof(ChatPage));
    }
}
