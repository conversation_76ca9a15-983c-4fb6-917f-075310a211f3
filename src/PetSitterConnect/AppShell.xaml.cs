using PetSitterConnect.Views;
using PetSitterConnect.ViewModels;

namespace PetSitterConnect;

public partial class AppShell : Shell
{
    public AppShell(AppShellViewModel viewModel)
    {
        InitializeComponent();
        BindingContext = viewModel;

        // Register routes for navigation
        Routing.RegisterRoute("login", typeof(LoginPage));
        Routing.RegisterRoute("register", typeof(RegisterPage));
        Routing.RegisterRoute("petdetail", typeof(PetDetailPage));
        Routing.RegisterRoute("createrequest", typeof(CreateRequestPage));
        Routing.RegisterRoute("chat", typeof(ChatPage));
        Routing.RegisterRoute("requestdetail", typeof(RequestDetailPage));
        Routing.RegisterRoute("bookingdetail", typeof(BookingDetailPage));
    }
}
