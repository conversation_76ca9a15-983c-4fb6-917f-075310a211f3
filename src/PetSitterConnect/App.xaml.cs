using PetSitterConnect.Services;
using PetSitterConnect.Views;

namespace PetSitterConnect;

public partial class App : Application
{
    public App()
    {
        InitializeComponent();
        
        // Set the main page based on authentication status
        SetMainPage();
    }

    private async void SetMainPage()
    {
        var authService = ServiceHelper.GetService<IAuthService>();
        
        if (await authService.IsAuthenticatedAsync())
        {
            MainPage = new AppShell();
        }
        else
        {
            MainPage = new NavigationPage(new LoginPage());
        }
    }
}

public static class ServiceHelper
{
    public static TService GetService<TService>()
        => Current.Handler.MauiContext.Services.GetService<TService>();
}
