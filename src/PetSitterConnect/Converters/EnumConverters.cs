using PetSitterConnect.Core.Models;
using System.Globalization;

namespace PetSitterConnect.Converters;

public class EnumToIntConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is Enum enumValue)
        {
            return (int)(object)enumValue;
        }
        return 0;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is int intValue && targetType.IsEnum)
        {
            return Enum.ToObject(targetType, intValue);
        }
        return Enum.GetValues(targetType).GetValue(0);
    }
}

public class StatusToColorConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is RequestStatus requestStatus)
        {
            return requestStatus switch
            {
                RequestStatus.Open => Colors.Green,
                RequestStatus.InProgress => Colors.Orange,
                RequestStatus.Completed => Colors.Blue,
                RequestStatus.Cancelled => Colors.Red,
                _ => Colors.Gray
            };
        }

        if (value is BookingStatus bookingStatus)
        {
            return bookingStatus switch
            {
                BookingStatus.Pending => Colors.Orange,
                BookingStatus.Confirmed => Colors.Green,
                BookingStatus.InProgress => Colors.Blue,
                BookingStatus.Completed => Colors.Purple,
                BookingStatus.Cancelled => Colors.Red,
                _ => Colors.Gray
            };
        }

        return Colors.Gray;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
